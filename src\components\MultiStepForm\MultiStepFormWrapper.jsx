import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import { FaArrowLeft, FaArrowRight, FaCheck, FaSave } from 'react-icons/fa';
import StepIndicator from './StepIndicator';

const MultiStepFormWrapper = ({
  steps,
  currentStep,
  onNext,
  onPrevious,
  onSubmit,
  isSubmitting = false,
  variant = 'customer',
  backUrl,
  title,
  subtitle,
  children,
  completedSteps = [],
  canProceed = true,
  showReview = false
}) => {
  const isFirstStep = currentStep === 0;
  const isLastStep = currentStep === steps.length - 1;
  const isReviewStep = showReview && currentStep === steps.length - 2;
  const isConfirmationStep = showReview && isLastStep;

  const getGradientColors = () => {
    if (variant === 'customer') {
      return 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)';
    } else {
      return 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)';
    }
  };

  const getButtonColor = () => {
    return variant === 'customer' ? 'btn-primary' : 'btn-success';
  };

  const getTextColor = () => {
    return variant === 'customer' ? 'text-primary' : 'text-success';
  };

  return (
    <div className="multi-step-form-wrapper">
      {/* Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-lg" style={{
            background: getGradientColors(),
            borderRadius: '20px'
          }}>
            <div className="card-body p-4">
              <div className="d-flex align-items-center text-white">
                <Link to={backUrl} className="btn btn-light btn-sm me-3 rounded-pill">
                  <FaArrowLeft className="me-2" />
                  Back to {variant === 'customer' ? 'Customers' : 'Shippers'}
                </Link>
                <div className="flex-grow-1">
                  <h1 className="h2 mb-2 text-white fw-bold">
                    {title}
                  </h1>
                  <p className="text-white-50 mb-0 fs-5">
                    {subtitle}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Step Indicator */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body py-4">
              <StepIndicator 
                steps={steps}
                currentStep={currentStep}
                completedSteps={completedSteps}
                variant={variant}
              />
            </div>
          </div>
        </div>
      </div>

      {/* Form Content */}
      <div className="row">
        <div className="col-12">
          <div className="card border-0 shadow-lg" style={{ borderRadius: '15px' }}>
            <div className="card-header border-0" style={{
              background: getGradientColors(),
              borderRadius: '15px 15px 0 0'
            }}>
              <div className="d-flex align-items-center text-white py-2">
                <h5 className="mb-0 fw-bold text-white">
                  Step {currentStep + 1}: {steps[currentStep]?.title}
                </h5>
                <div className="ms-auto">
                  <span className="badge bg-white bg-opacity-20 text-white px-3 py-2 rounded-pill">
                    {currentStep + 1} of {steps.length}
                  </span>
                </div>
              </div>
            </div>
            <div className="card-body p-4">
              {children}
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="row mt-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body p-4">
              <div className="d-flex justify-content-between align-items-center">
                <div>
                  {!isFirstStep && (
                    <button
                      type="button"
                      className="btn btn-outline-secondary btn-lg px-4"
                      onClick={onPrevious}
                      style={{ borderRadius: '12px' }}
                    >
                      <FaArrowLeft className="me-2" />
                      Previous
                    </button>
                  )}
                </div>
                
                <div className="d-flex gap-3">
                  {isConfirmationStep ? (
                    <button
                      type="button"
                      className={`btn ${getButtonColor()} btn-lg px-5 fw-bold`}
                      onClick={onSubmit}
                      disabled={isSubmitting}
                      style={{ 
                        borderRadius: '12px',
                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)'
                      }}
                    >
                      {isSubmitting ? (
                        <>
                          <div className="spinner-border spinner-border-sm me-2" role="status">
                            <span className="visually-hidden">Loading...</span>
                          </div>
                          Creating...
                        </>
                      ) : (
                        <>
                          <FaSave className="me-2" />
                          Create {variant === 'customer' ? 'Customer' : 'Shipper'}
                        </>
                      )}
                    </button>
                  ) : isReviewStep ? (
                    <button
                      type="button"
                      className={`btn ${getButtonColor()} btn-lg px-4`}
                      onClick={onNext}
                      style={{ borderRadius: '12px' }}
                    >
                      <FaCheck className="me-2" />
                      Confirm & Submit
                    </button>
                  ) : !isLastStep ? (
                    <button
                      type="button"
                      className={`btn ${getButtonColor()} btn-lg px-4`}
                      onClick={onNext}
                      disabled={!canProceed}
                      style={{ borderRadius: '12px' }}
                    >
                      Continue
                      <FaArrowRight className="ms-2" />
                    </button>
                  ) : null}
                </div>
              </div>
              
              {/* Progress Info */}
              <div className="mt-3 pt-3 border-top">
                <div className="row text-center">
                  <div className="col-md-4">
                    <small className={`fw-semibold ${getTextColor()}`}>
                      Progress: {Math.round(((currentStep + 1) / steps.length) * 100)}%
                    </small>
                  </div>
                  <div className="col-md-4">
                    <small className="text-muted">
                      Completed: {completedSteps.length} of {steps.length} steps
                    </small>
                  </div>
                  <div className="col-md-4">
                    <small className="text-muted">
                      Estimated time: {steps.length - currentStep - 1} min remaining
                    </small>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MultiStepFormWrapper;
