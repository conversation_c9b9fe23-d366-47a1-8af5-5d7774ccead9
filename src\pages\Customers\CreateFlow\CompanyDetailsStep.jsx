import React from 'react';
import { 
  FaBuilding, 
  FaChartLine, 
  FaShieldAlt, 
  FaIdCard, 
  FaGlobe,
  FaInfoCircle 
} from 'react-icons/fa';

const CompanyDetailsStep = ({ formik }) => {
  return (
    <div className="company-details-step">
      <div className="row">
        <div className="col-lg-8">
          <div className="mb-4">
            <h4 className="text-primary fw-bold mb-2">
              <FaBuilding className="me-2" />
              Company Information
            </h4>
            <p className="text-muted">
              Enter the basic company details for your new customer. This information will be used for invoicing and communication.
            </p>
          </div>

          <div className="row g-4">
            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="text"
                  className={`form-control ${formik.touched.companyName && formik.errors.companyName ? 'is-invalid' : ''}`}
                  id="companyName"
                  name="companyName"
                  value={formik.values.companyName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="Enter company name"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="companyName" className="fw-semibold">
                  <FaBuilding className="me-2 text-primary" />
                  Company Name *
                </label>
                {formik.touched.companyName && formik.errors.companyName && (
                  <div className="invalid-feedback">{formik.errors.companyName}</div>
                )}
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <select
                  className="form-select"
                  id="customerType"
                  name="customerType"
                  value={formik.values.customerType}
                  onChange={formik.handleChange}
                  style={{ borderRadius: '10px' }}
                >
                  <option value="regular">🏢 Regular Customer</option>
                  <option value="premium">⭐ Premium Customer</option>
                  <option value="enterprise">🏭 Enterprise Customer</option>
                </select>
                <label htmlFor="customerType" className="fw-semibold">
                  <FaChartLine className="me-2 text-success" />
                  Customer Type
                </label>
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="text"
                  className={`form-control ${formik.touched.gstNumber && formik.errors.gstNumber ? 'is-invalid' : ''}`}
                  id="gstNumber"
                  name="gstNumber"
                  value={formik.values.gstNumber}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="22AAAAA0000A1Z5"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="gstNumber" className="fw-semibold">
                  <FaShieldAlt className="me-2 text-warning" />
                  GST Number
                </label>
                {formik.touched.gstNumber && formik.errors.gstNumber && (
                  <div className="invalid-feedback">{formik.errors.gstNumber}</div>
                )}
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="text"
                  className={`form-control ${formik.touched.panNumber && formik.errors.panNumber ? 'is-invalid' : ''}`}
                  id="panNumber"
                  name="panNumber"
                  value={formik.values.panNumber}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="**********"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="panNumber" className="fw-semibold">
                  <FaIdCard className="me-2 text-info" />
                  PAN Number
                </label>
                {formik.touched.panNumber && formik.errors.panNumber && (
                  <div className="invalid-feedback">{formik.errors.panNumber}</div>
                )}
              </div>
            </div>

            <div className="col-12">
              <div className="form-floating">
                <input
                  type="url"
                  className={`form-control ${formik.touched.website && formik.errors.website ? 'is-invalid' : ''}`}
                  id="website"
                  name="website"
                  value={formik.values.website}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="https://www.company.com"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="website" className="fw-semibold">
                  <FaGlobe className="me-2 text-primary" />
                  Company Website (Optional)
                </label>
                {formik.touched.website && formik.errors.website && (
                  <div className="invalid-feedback">{formik.errors.website}</div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-4">
          {/* Help Section */}
          <div className="card border-0 bg-light">
            <div className="card-body p-4">
              <h6 className="fw-bold text-primary mb-3">
                <FaInfoCircle className="me-2" />
                Quick Tips
              </h6>
              <ul className="list-unstyled mb-0 small">
                <li className="mb-2">
                  <strong>Company Name:</strong> Enter the legal business name as it appears on official documents.
                </li>
                <li className="mb-2">
                  <strong>Customer Type:</strong> Choose based on business volume and relationship level.
                </li>
                <li className="mb-2">
                  <strong>GST Number:</strong> 15-digit number starting with state code (optional but recommended).
                </li>
                <li className="mb-2">
                  <strong>PAN Number:</strong> 10-character alphanumeric code for tax identification.
                </li>
              </ul>
            </div>
          </div>

          {/* Customer Type Info */}
          <div className="card border-0 bg-primary bg-opacity-10 mt-3">
            <div className="card-body p-4">
              <h6 className="fw-bold text-primary mb-3">Customer Types</h6>
              <div className="small">
                <div className="mb-2">
                  <strong>🏢 Regular:</strong> Standard customers with basic service levels
                </div>
                <div className="mb-2">
                  <strong>⭐ Premium:</strong> High-value customers with priority support
                </div>
                <div className="mb-2">
                  <strong>🏭 Enterprise:</strong> Large corporations with custom solutions
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyDetailsStep;
