import React, { useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaSearch,
  FaFilter,
  FaDownload,
  FaTruck,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope,
  FaShippingFast,
  FaStar
} from 'react-icons/fa';
import ROUTES from '@constants/routes';

const Shippers = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock data for shippers
  const [shippers] = useState([
    {
      id: 1,
      name: 'Express Logistics Pvt Ltd',
      contactPerson: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+91 98765 43210',
      address: 'Mumbai, Maharashtra',
      status: 'active',
      serviceType: 'express',
      totalShipments: 156,
      rating: 4.8,
      lastShipment: '2024-01-18',
      vehicleCount: 25
    },
    {
      id: 2,
      name: 'Swift Transport Solutions',
      contactPerson: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+91 87654 32109',
      address: 'Delhi, NCR',
      status: 'active',
      serviceType: 'standard',
      totalShipments: 89,
      rating: 4.5,
      lastShipment: '2024-01-16',
      vehicleCount: 18
    },
    {
      id: 3,
      name: 'Reliable Cargo Services',
      contactPerson: 'Amit Patel',
      email: '<EMAIL>',
      phone: '+91 76543 21098',
      address: 'Ahmedabad, Gujarat',
      status: 'inactive',
      serviceType: 'heavy',
      totalShipments: 67,
      rating: 4.2,
      lastShipment: '2023-12-28',
      vehicleCount: 12
    },
    {
      id: 4,
      name: 'Metro Freight Movers',
      contactPerson: 'Sunita Reddy',
      email: '<EMAIL>',
      phone: '+91 65432 10987',
      address: 'Hyderabad, Telangana',
      status: 'active',
      serviceType: 'express',
      totalShipments: 203,
      rating: 4.9,
      lastShipment: '2024-01-19',
      vehicleCount: 35
    },
    {
      id: 5,
      name: 'Coastal Shipping Co.',
      contactPerson: 'Vikram Singh',
      email: '<EMAIL>',
      phone: '+91 54321 09876',
      address: 'Chennai, Tamil Nadu',
      status: 'active',
      serviceType: 'standard',
      totalShipments: 124,
      rating: 4.6,
      lastShipment: '2024-01-17',
      vehicleCount: 22
    }
  ]);

  // Filter shippers based on search and status
  const filteredShippers = shippers.filter(shipper => {
    const matchesSearch = shipper.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         shipper.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         shipper.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || shipper.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status) => {
    return status === 'active' 
      ? <span className="badge bg-success">Active</span>
      : <span className="badge bg-secondary">Inactive</span>;
  };

  const getServiceTypeBadge = (type) => {
    const badges = {
      express: <span className="badge bg-danger">Express</span>,
      standard: <span className="badge bg-primary">Standard</span>,
      heavy: <span className="badge bg-warning">Heavy Cargo</span>
    };
    return badges[type] || <span className="badge bg-secondary">Unknown</span>;
  };

  const getRatingStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<FaStar key={i} className="text-warning" size={12} />);
    }
    
    if (hasHalfStar) {
      stars.push(<FaStar key="half" className="text-warning opacity-50" size={12} />);
    }

    return stars;
  };

  return (
    <div className="shippers-page">
      {/* Page Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="h3 mb-1 text-dark fw-bold">
                <FaTruck className="me-2 text-primary" />
                Shippers Management
              </h1>
              <p className="text-muted mb-0">Manage your shipping partners and logistics providers</p>
            </div>
            <div className="d-flex gap-2">
              <button className="btn btn-outline-primary">
                <FaDownload className="me-2" />
                Export
              </button>
              <Link to={ROUTES.SHIPPERS_CREATE} className="btn btn-primary">
                <FaPlus className="me-2" />
                Add Shipper
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-6">
                  <div className="position-relative">
                    <FaSearch className="position-absolute top-50 start-0 translate-middle-y ms-3 text-muted" />
                    <input
                      type="text"
                      className="form-control ps-5"
                      placeholder="Search shippers by name, contact person, or email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <div className="col-md-3">
                  <select
                    className="form-select"
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
                <div className="col-md-3">
                  <button className="btn btn-outline-secondary w-100">
                    <FaFilter className="me-2" />
                    More Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="row mb-4">
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 rounded-circle p-3">
                    <FaTruck className="text-primary" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{shippers.length}</div>
                  <div className="text-muted small">Total Shippers</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 rounded-circle p-3">
                    <FaTruck className="text-success" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{shippers.filter(s => s.status === 'active').length}</div>
                  <div className="text-muted small">Active Shippers</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 rounded-circle p-3">
                    <FaShippingFast className="text-warning" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{shippers.reduce((sum, s) => sum + s.totalShipments, 0)}</div>
                  <div className="text-muted small">Total Shipments</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 rounded-circle p-3">
                    <FaTruck className="text-info" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{shippers.reduce((sum, s) => sum + s.vehicleCount, 0)}</div>
                  <div className="text-muted small">Total Vehicles</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Shippers Table */}
      <div className="row">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-white border-bottom">
              <h5 className="mb-0 fw-semibold">
                Shippers List ({filteredShippers.length})
              </h5>
            </div>
            <div className="card-body p-0">
              <div className="table-responsive">
                <table className="table table-hover mb-0">
                  <thead className="table-light">
                    <tr>
                      <th className="border-0 fw-semibold">Shipper</th>
                      <th className="border-0 fw-semibold">Contact Info</th>
                      <th className="border-0 fw-semibold">Location</th>
                      <th className="border-0 fw-semibold">Service Type</th>
                      <th className="border-0 fw-semibold">Performance</th>
                      <th className="border-0 fw-semibold">Rating</th>
                      <th className="border-0 fw-semibold">Status</th>
                      <th className="border-0 fw-semibold">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredShippers.map((shipper) => (
                      <tr key={shipper.id}>
                        <td>
                          <div>
                            <div className="fw-semibold text-dark">{shipper.name}</div>
                            <small className="text-muted">{shipper.contactPerson}</small>
                          </div>
                        </td>
                        <td>
                          <div>
                            <div className="d-flex align-items-center mb-1">
                              <FaEnvelope className="text-muted me-2" size={12} />
                              <small>{shipper.email}</small>
                            </div>
                            <div className="d-flex align-items-center">
                              <FaPhone className="text-muted me-2" size={12} />
                              <small>{shipper.phone}</small>
                            </div>
                          </div>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <FaMapMarkerAlt className="text-muted me-2" size={12} />
                            <small>{shipper.address}</small>
                          </div>
                        </td>
                        <td>
                          {getServiceTypeBadge(shipper.serviceType)}
                        </td>
                        <td>
                          <div>
                            <div className="fw-semibold">{shipper.totalShipments} shipments</div>
                            <small className="text-muted">{shipper.vehicleCount} vehicles</small>
                          </div>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <div className="me-2">
                              {getRatingStars(shipper.rating)}
                            </div>
                            <small className="fw-semibold">{shipper.rating}</small>
                          </div>
                        </td>
                        <td>
                          {getStatusBadge(shipper.status)}
                        </td>
                        <td>
                          <div className="d-flex gap-1">
                            <button className="btn btn-sm btn-outline-primary" title="View Details">
                              <FaEye size={12} />
                            </button>
                            <button className="btn btn-sm btn-outline-warning" title="Edit Shipper">
                              <FaEdit size={12} />
                            </button>
                            <button className="btn btn-sm btn-outline-danger" title="Delete Shipper">
                              <FaTrash size={12} />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Shippers;
