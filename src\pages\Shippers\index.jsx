import React, { useState, useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';
import DataTable from 'react-data-table-component';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaSearch,
  FaFilter,
  FaDownload,
  FaTruck,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope,
  FaShippingFast,
  FaStar
} from 'react-icons/fa';
import ROUTES from '@constants/routes';

const Shippers = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock data for shippers
  const [shippers] = useState([
    {
      id: 1,
      name: 'Express Logistics Pvt Ltd',
      contactPerson: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+91 98765 43210',
      address: 'Mumbai, Maharashtra',
      status: 'active',
      serviceType: 'express',
      totalShipments: 156,
      rating: 4.8,
      lastShipment: '2024-01-18',
      vehicleCount: 25
    },
    {
      id: 2,
      name: 'Swift Transport Solutions',
      contactPerson: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+91 87654 32109',
      address: 'Delhi, NCR',
      status: 'active',
      serviceType: 'standard',
      totalShipments: 89,
      rating: 4.5,
      lastShipment: '2024-01-16',
      vehicleCount: 18
    },
    {
      id: 3,
      name: 'Reliable Cargo Services',
      contactPerson: 'Amit Patel',
      email: '<EMAIL>',
      phone: '+91 76543 21098',
      address: 'Ahmedabad, Gujarat',
      status: 'inactive',
      serviceType: 'heavy',
      totalShipments: 67,
      rating: 4.2,
      lastShipment: '2023-12-28',
      vehicleCount: 12
    },
    {
      id: 4,
      name: 'Metro Freight Movers',
      contactPerson: 'Sunita Reddy',
      email: '<EMAIL>',
      phone: '+91 65432 10987',
      address: 'Hyderabad, Telangana',
      status: 'active',
      serviceType: 'express',
      totalShipments: 203,
      rating: 4.9,
      lastShipment: '2024-01-19',
      vehicleCount: 35
    },
    {
      id: 5,
      name: 'Coastal Shipping Co.',
      contactPerson: 'Vikram Singh',
      email: '<EMAIL>',
      phone: '+91 54321 09876',
      address: 'Chennai, Tamil Nadu',
      status: 'active',
      serviceType: 'standard',
      totalShipments: 124,
      rating: 4.6,
      lastShipment: '2024-01-17',
      vehicleCount: 22
    }
  ]);

  // Filter shippers based on search and status
  const filteredShippers = shippers.filter(shipper => {
    const matchesSearch = shipper.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         shipper.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         shipper.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || shipper.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status) => {
    return status === 'active'
      ? <span className="badge bg-success">Active</span>
      : <span className="badge bg-secondary">Inactive</span>;
  };

  const getServiceTypeBadge = (type) => {
    const badges = {
      express: <span className="badge bg-danger">Express</span>,
      standard: <span className="badge bg-primary">Standard</span>,
      heavy: <span className="badge bg-warning">Heavy Cargo</span>
    };
    return badges[type] || <span className="badge bg-secondary">Unknown</span>;
  };

  const getRatingStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<FaStar key={i} className="text-warning" size={12} />);
    }

    if (hasHalfStar) {
      stars.push(<FaStar key="half" className="text-warning opacity-50" size={12} />);
    }

    return stars;
  };

  // DataTable columns configuration
  const columns = useMemo(() => [
    {
      name: 'Shipper',
      selector: row => row.name,
      sortable: true,
      minWidth: '200px',
      cell: row => (
        <div>
          <div className="fw-semibold text-dark">{row.name}</div>
          <small className="text-muted">{row.contactPerson}</small>
        </div>
      ),
    },
    {
      name: 'Contact Info',
      selector: row => row.email,
      sortable: true,
      minWidth: '220px',
      cell: row => (
        <div>
          <div className="d-flex align-items-center mb-1">
            <FaEnvelope className="text-muted me-2" size={12} />
            <small>{row.email}</small>
          </div>
          <div className="d-flex align-items-center">
            <FaPhone className="text-muted me-2" size={12} />
            <small>{row.phone}</small>
          </div>
        </div>
      ),
    },
    {
      name: 'Location',
      selector: row => row.address,
      sortable: true,
      minWidth: '150px',
      cell: row => (
        <div className="d-flex align-items-center">
          <FaMapMarkerAlt className="text-muted me-2" size={12} />
          <small>{row.address}</small>
        </div>
      ),
    },
    {
      name: 'Service Type',
      selector: row => row.serviceType,
      sortable: true,
      minWidth: '120px',
      cell: row => getServiceTypeBadge(row.serviceType),
    },
    {
      name: 'Performance',
      selector: row => row.totalShipments,
      sortable: true,
      minWidth: '120px',
      cell: row => (
        <div>
          <div className="fw-semibold">{row.totalShipments} shipments</div>
          <small className="text-muted">{row.vehicleCount} vehicles</small>
        </div>
      ),
    },
    {
      name: 'Rating',
      selector: row => row.rating,
      sortable: true,
      minWidth: '100px',
      cell: row => (
        <div className="d-flex align-items-center">
          <div className="me-2">
            {getRatingStars(row.rating)}
          </div>
          <small className="fw-semibold">{row.rating}</small>
        </div>
      ),
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      minWidth: '100px',
      cell: row => getStatusBadge(row.status),
    },
    {
      name: 'Actions',
      minWidth: '120px',
      cell: row => (
        <div className="d-flex gap-1">
          <button className="btn btn-sm btn-outline-primary" title="View Details">
            <FaEye size={12} />
          </button>
          <button className="btn btn-sm btn-outline-warning" title="Edit Shipper">
            <FaEdit size={12} />
          </button>
          <button className="btn btn-sm btn-outline-danger" title="Delete Shipper">
            <FaTrash size={12} />
          </button>
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ], []);

  // Custom styles for DataTable
  const customStyles = {
    header: {
      style: {
        minHeight: '56px',
      },
    },
    headRow: {
      style: {
        borderTopStyle: 'solid',
        borderTopWidth: '1px',
        borderTopColor: '#e9ecef',
        backgroundColor: '#f8f9fa',
      },
    },
    headCells: {
      style: {
        '&:not(:last-of-type)': {
          borderRightStyle: 'solid',
          borderRightWidth: '1px',
          borderRightColor: '#e9ecef',
        },
        fontSize: '14px',
        fontWeight: '600',
        color: '#495057',
      },
    },
    cells: {
      style: {
        '&:not(:last-of-type)': {
          borderRightStyle: 'solid',
          borderRightWidth: '1px',
          borderRightColor: '#e9ecef',
        },
        fontSize: '13px',
        padding: '12px 8px',
      },
    },
    rows: {
      style: {
        minHeight: '60px',
        '&:hover': {
          backgroundColor: '#f8f9fa',
        },
      },
    },
  };

  return (
    <div className="shippers-page">
      {/* Modern Page Header with Gradient Background */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-lg" style={{
            background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
            borderRadius: '20px'
          }}>
            <div className="card-body p-4">
              <div className="d-flex justify-content-between align-items-center text-white">
                <div className="d-flex align-items-center">
                  <div className="bg-white bg-opacity-20 rounded-circle p-3 me-4">
                    <FaTruck className="text-white" size={32} />
                  </div>
                  <div>
                    <h1 className="h2 mb-2 text-white fw-bold">
                      Shipping Partners Hub
                    </h1>
                    <p className="text-white-50 mb-0 fs-5">
                      Manage your trusted logistics network and delivery partners
                    </p>
                  </div>
                </div>
                <div className="d-flex gap-3">
                  <button className="btn btn-light btn-lg rounded-pill px-4">
                    <FaDownload className="me-2" />
                    Export Data
                  </button>
                  <Link to={ROUTES.SHIPPERS_CREATE} className="btn btn-warning btn-lg rounded-pill px-4 fw-bold">
                    <FaPlus className="me-2" />
                    Add New Partner
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Search and Filters */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-lg" style={{ borderRadius: '15px' }}>
            <div className="card-body p-4">
              <div className="row g-4">
                <div className="col-lg-6">
                  <div className="position-relative">
                    <div className="input-group input-group-lg">
                      <span className="input-group-text bg-success text-white border-0" style={{ borderRadius: '12px 0 0 12px' }}>
                        <FaSearch size={18} />
                      </span>
                      <input
                        type="text"
                        className="form-control border-0 shadow-sm"
                        placeholder="Search shipping partners by name, contact person, or email..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        style={{
                          borderRadius: '0 12px 12px 0',
                          fontSize: '16px',
                          padding: '12px 16px'
                        }}
                      />
                    </div>
                  </div>
                </div>
                <div className="col-lg-3">
                  <select
                    className="form-select form-select-lg border-0 shadow-sm"
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    style={{
                      borderRadius: '12px',
                      fontSize: '16px',
                      padding: '12px 16px'
                    }}
                  >
                    <option value="all">🔍 All Status</option>
                    <option value="active">✅ Active Partners</option>
                    <option value="inactive">⏸️ Inactive Partners</option>
                  </select>
                </div>
                <div className="col-lg-3">
                  <button className="btn btn-outline-success btn-lg w-100 fw-semibold" style={{ borderRadius: '12px' }}>
                    <FaFilter className="me-2" />
                    Advanced Filters
                  </button>
                </div>
              </div>

              {/* Quick Filter Tags */}
              <div className="mt-3">
                <div className="d-flex flex-wrap gap-2">
                  <span className="badge bg-light text-dark px-3 py-2 rounded-pill">
                    <FaTruck className="me-1" />
                    All Partners ({shippers.length})
                  </span>
                  <span className="badge bg-success-subtle text-success px-3 py-2 rounded-pill">
                    <FaTruck className="me-1" />
                    Active ({shippers.filter(s => s.status === 'active').length})
                  </span>
                  <span className="badge bg-warning-subtle text-warning px-3 py-2 rounded-pill">
                    <FaShippingFast className="me-1" />
                    Express Service
                  </span>
                  <span className="badge bg-info-subtle text-info px-3 py-2 rounded-pill">
                    <FaTruck className="me-1" />
                    High Rated (4.5+)
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="row mb-4">
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-lg h-100" style={{
            borderRadius: '15px',
            background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)'
          }}>
            <div className="card-body p-4 text-white">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <div className="h2 mb-1 fw-bold">{shippers.length}</div>
                  <div className="text-white-50 fw-semibold">Total Partners</div>
                  <div className="mt-2">
                    <small className="text-white-50">📈 +8% this month</small>
                  </div>
                </div>
                <div className="bg-white bg-opacity-20 rounded-circle p-3">
                  <FaTruck className="text-white" size={28} />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-lg h-100" style={{
            borderRadius: '15px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
          }}>
            <div className="card-body p-4 text-white">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <div className="h2 mb-1 fw-bold">{shippers.filter(s => s.status === 'active').length}</div>
                  <div className="text-white-50 fw-semibold">Active Partners</div>
                  <div className="mt-2">
                    <small className="text-white-50">✅ {Math.round((shippers.filter(s => s.status === 'active').length / shippers.length) * 100)}% active rate</small>
                  </div>
                </div>
                <div className="bg-white bg-opacity-20 rounded-circle p-3">
                  <FaTruck className="text-white" size={28} />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-lg h-100" style={{
            borderRadius: '15px',
            background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
          }}>
            <div className="card-body p-4 text-white">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <div className="h2 mb-1 fw-bold">{shippers.reduce((sum, s) => sum + s.totalShipments, 0)}</div>
                  <div className="text-white-50 fw-semibold">Total Shipments</div>
                  <div className="mt-2">
                    <small className="text-white-50">📦 Avg: {Math.round(shippers.reduce((sum, s) => sum + s.totalShipments, 0) / shippers.length)} per partner</small>
                  </div>
                </div>
                <div className="bg-white bg-opacity-20 rounded-circle p-3">
                  <FaShippingFast className="text-white" size={28} />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-lg h-100" style={{
            borderRadius: '15px',
            background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
          }}>
            <div className="card-body p-4 text-white">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <div className="h2 mb-1 fw-bold">{shippers.reduce((sum, s) => sum + s.vehicleCount, 0)}</div>
                  <div className="text-white-50 fw-semibold">Total Fleet</div>
                  <div className="mt-2">
                    <small className="text-white-50">🚛 Avg: {Math.round(shippers.reduce((sum, s) => sum + s.vehicleCount, 0) / shippers.length)} vehicles</small>
                  </div>
                </div>
                <div className="bg-white bg-opacity-20 rounded-circle p-3">
                  <FaTruck className="text-white" size={28} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Shippers Table */}
      <div className="row">
        <div className="col-12">
          <div className="card border-0 shadow-lg" style={{ borderRadius: '15px' }}>
            <div className="card-header border-0" style={{
              background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
              borderRadius: '15px 15px 0 0'
            }}>
              <div className="d-flex justify-content-between align-items-center text-white py-2">
                <div className="d-flex align-items-center">
                  <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                    <FaTruck className="text-white" size={20} />
                  </div>
                  <h5 className="mb-0 fw-bold text-white">
                    Shipping Partners Database ({filteredShippers.length} records)
                  </h5>
                </div>
                <div className="d-flex gap-2">
                  <span className="badge bg-white bg-opacity-20 text-white px-3 py-2 rounded-pill">
                    📊 Live Data
                  </span>
                  <span className="badge bg-success bg-opacity-20 text-white px-3 py-2 rounded-pill">
                    ✅ Real-time Updates
                  </span>
                </div>
              </div>
            </div>
            <div className="card-body p-0">
              <div className="logistics-table shippers-table">
                <DataTable
                  columns={columns}
                  data={filteredShippers}
                  pagination
                  paginationPerPage={10}
                  paginationRowsPerPageOptions={[5, 10, 15, 20]}
                  responsive
                  highlightOnHover
                  striped
                  customStyles={customStyles}
                  noDataComponent={
                    <div className="text-center py-5">
                      <div className="mb-3">
                        <FaTruck className="text-muted" size={64} />
                      </div>
                      <h5 className="text-muted mb-2">No shipping partners found</h5>
                      <p className="text-muted mb-3">Try adjusting your search criteria or add a new shipping partner</p>
                      <Link to={ROUTES.SHIPPERS_CREATE} className="btn btn-success rounded-pill px-4">
                        <FaPlus className="me-2" />
                        Add First Partner
                      </Link>
                    </div>
                  }
                  progressPending={false}
                  progressComponent={
                    <div className="text-center py-5">
                      <div className="spinner-border text-success mb-3" role="status" style={{ width: '3rem', height: '3rem' }}>
                        <span className="visually-hidden">Loading...</span>
                      </div>
                      <h5 className="text-success">Loading shipping partners...</h5>
                      <p className="text-muted">Please wait while we fetch the latest information</p>
                    </div>
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Shippers;
