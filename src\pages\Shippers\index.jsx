import React, { useState, useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';
import DataTable from 'react-data-table-component';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaSearch,
  FaFilter,
  FaDownload,
  FaTruck,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope,
  FaShippingFast,
  FaStar
} from 'react-icons/fa';
import ROUTES from '@constants/routes';

const Shippers = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock data for shippers
  const [shippers] = useState([
    {
      id: 1,
      name: 'Express Logistics Pvt Ltd',
      contactPerson: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+91 98765 43210',
      address: 'Mumbai, Maharashtra',
      status: 'active',
      serviceType: 'express',
      totalShipments: 156,
      rating: 4.8,
      lastShipment: '2024-01-18',
      vehicleCount: 25
    },
    {
      id: 2,
      name: 'Swift Transport Solutions',
      contactPerson: '<PERSON><PERSON>',
      email: '<EMAIL>',
      phone: '+91 87654 32109',
      address: 'Delhi, NCR',
      status: 'active',
      serviceType: 'standard',
      totalShipments: 89,
      rating: 4.5,
      lastShipment: '2024-01-16',
      vehicleCount: 18
    },
    {
      id: 3,
      name: 'Reliable Cargo Services',
      contactPerson: 'Amit Patel',
      email: '<EMAIL>',
      phone: '+91 76543 21098',
      address: 'Ahmedabad, Gujarat',
      status: 'inactive',
      serviceType: 'heavy',
      totalShipments: 67,
      rating: 4.2,
      lastShipment: '2023-12-28',
      vehicleCount: 12
    },
    {
      id: 4,
      name: 'Metro Freight Movers',
      contactPerson: 'Sunita Reddy',
      email: '<EMAIL>',
      phone: '+91 65432 10987',
      address: 'Hyderabad, Telangana',
      status: 'active',
      serviceType: 'express',
      totalShipments: 203,
      rating: 4.9,
      lastShipment: '2024-01-19',
      vehicleCount: 35
    },
    {
      id: 5,
      name: 'Coastal Shipping Co.',
      contactPerson: 'Vikram Singh',
      email: '<EMAIL>',
      phone: '+91 54321 09876',
      address: 'Chennai, Tamil Nadu',
      status: 'active',
      serviceType: 'standard',
      totalShipments: 124,
      rating: 4.6,
      lastShipment: '2024-01-17',
      vehicleCount: 22
    }
  ]);

  // Filter shippers based on search and status
  const filteredShippers = shippers.filter(shipper => {
    const matchesSearch = shipper.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         shipper.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         shipper.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || shipper.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status) => {
    return status === 'active'
      ? <span className="badge bg-success">Active</span>
      : <span className="badge bg-secondary">Inactive</span>;
  };

  const getServiceTypeBadge = (type) => {
    const badges = {
      express: <span className="badge bg-danger">Express</span>,
      standard: <span className="badge bg-primary">Standard</span>,
      heavy: <span className="badge bg-warning">Heavy Cargo</span>
    };
    return badges[type] || <span className="badge bg-secondary">Unknown</span>;
  };

  const getRatingStars = (rating) => {
    const stars = [];
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 !== 0;

    for (let i = 0; i < fullStars; i++) {
      stars.push(<FaStar key={i} className="text-warning" size={12} />);
    }

    if (hasHalfStar) {
      stars.push(<FaStar key="half" className="text-warning opacity-50" size={12} />);
    }

    return stars;
  };

  // DataTable columns configuration
  const columns = useMemo(() => [
    {
      name: 'Shipper',
      selector: row => row.name,
      sortable: true,
      minWidth: '200px',
      cell: row => (
        <div>
          <div className="fw-semibold text-dark">{row.name}</div>
          <small className="text-muted">{row.contactPerson}</small>
        </div>
      ),
    },
    {
      name: 'Contact Info',
      selector: row => row.email,
      sortable: true,
      minWidth: '220px',
      cell: row => (
        <div>
          <div className="d-flex align-items-center mb-1">
            <FaEnvelope className="text-muted me-2" size={12} />
            <small>{row.email}</small>
          </div>
          <div className="d-flex align-items-center">
            <FaPhone className="text-muted me-2" size={12} />
            <small>{row.phone}</small>
          </div>
        </div>
      ),
    },
    {
      name: 'Location',
      selector: row => row.address,
      sortable: true,
      minWidth: '150px',
      cell: row => (
        <div className="d-flex align-items-center">
          <FaMapMarkerAlt className="text-muted me-2" size={12} />
          <small>{row.address}</small>
        </div>
      ),
    },
    {
      name: 'Service Type',
      selector: row => row.serviceType,
      sortable: true,
      minWidth: '120px',
      cell: row => getServiceTypeBadge(row.serviceType),
    },
    {
      name: 'Performance',
      selector: row => row.totalShipments,
      sortable: true,
      minWidth: '120px',
      cell: row => (
        <div>
          <div className="fw-semibold">{row.totalShipments} shipments</div>
          <small className="text-muted">{row.vehicleCount} vehicles</small>
        </div>
      ),
    },
    {
      name: 'Rating',
      selector: row => row.rating,
      sortable: true,
      minWidth: '100px',
      cell: row => (
        <div className="d-flex align-items-center">
          <div className="me-2">
            {getRatingStars(row.rating)}
          </div>
          <small className="fw-semibold">{row.rating}</small>
        </div>
      ),
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      minWidth: '100px',
      cell: row => getStatusBadge(row.status),
    },
    {
      name: 'Actions',
      minWidth: '120px',
      cell: row => (
        <div className="d-flex gap-1">
          <button className="btn btn-sm btn-outline-primary" title="View Details">
            <FaEye size={12} />
          </button>
          <button className="btn btn-sm btn-outline-warning" title="Edit Shipper">
            <FaEdit size={12} />
          </button>
          <button className="btn btn-sm btn-outline-danger" title="Delete Shipper">
            <FaTrash size={12} />
          </button>
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ], []);

  // Custom styles for DataTable
  const customStyles = {
    header: {
      style: {
        minHeight: '56px',
      },
    },
    headRow: {
      style: {
        borderTopStyle: 'solid',
        borderTopWidth: '1px',
        borderTopColor: '#e9ecef',
        backgroundColor: '#f8f9fa',
      },
    },
    headCells: {
      style: {
        '&:not(:last-of-type)': {
          borderRightStyle: 'solid',
          borderRightWidth: '1px',
          borderRightColor: '#e9ecef',
        },
        fontSize: '14px',
        fontWeight: '600',
        color: '#495057',
      },
    },
    cells: {
      style: {
        '&:not(:last-of-type)': {
          borderRightStyle: 'solid',
          borderRightWidth: '1px',
          borderRightColor: '#e9ecef',
        },
        fontSize: '13px',
        padding: '12px 8px',
      },
    },
    rows: {
      style: {
        minHeight: '60px',
        '&:hover': {
          backgroundColor: '#f8f9fa',
        },
      },
    },
  };

  return (
    <div className="shippers-page">
      {/* Page Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="h3 mb-1 text-dark fw-bold">
                <FaTruck className="me-2 text-primary" />
                Shippers Management
              </h1>
              <p className="text-muted mb-0">Manage your shipping partners and logistics providers</p>
            </div>
            <div className="d-flex gap-2">
              <button className="btn btn-outline-primary">
                <FaDownload className="me-2" />
                Export
              </button>
              <Link to={ROUTES.SHIPPERS_CREATE} className="btn btn-primary">
                <FaPlus className="me-2" />
                Add Shipper
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-6">
                  <div className="position-relative">
                    <FaSearch className="position-absolute top-50 start-0 translate-middle-y ms-3 text-muted" />
                    <input
                      type="text"
                      className="form-control ps-5"
                      placeholder="Search shippers by name, contact person, or email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <div className="col-md-3">
                  <select
                    className="form-select"
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
                <div className="col-md-3">
                  <button className="btn btn-outline-secondary w-100">
                    <FaFilter className="me-2" />
                    More Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="row mb-4">
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 rounded-circle p-3">
                    <FaTruck className="text-primary" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{shippers.length}</div>
                  <div className="text-muted small">Total Shippers</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 rounded-circle p-3">
                    <FaTruck className="text-success" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{shippers.filter(s => s.status === 'active').length}</div>
                  <div className="text-muted small">Active Shippers</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 rounded-circle p-3">
                    <FaShippingFast className="text-warning" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{shippers.reduce((sum, s) => sum + s.totalShipments, 0)}</div>
                  <div className="text-muted small">Total Shipments</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 rounded-circle p-3">
                    <FaTruck className="text-info" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{shippers.reduce((sum, s) => sum + s.vehicleCount, 0)}</div>
                  <div className="text-muted small">Total Vehicles</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Shippers Table */}
      <div className="row">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-white border-bottom">
              <h5 className="mb-0 fw-semibold">
                Shippers List ({filteredShippers.length})
              </h5>
            </div>
            <div className="card-body p-0">
              <div className="logistics-table shippers-table">
                <DataTable
                  columns={columns}
                  data={filteredShippers}
                  pagination
                  paginationPerPage={10}
                  paginationRowsPerPageOptions={[5, 10, 15, 20]}
                  responsive
                  highlightOnHover
                  striped
                  customStyles={customStyles}
                  noDataComponent={
                    <div className="text-center py-4">
                      <FaTruck className="text-muted mb-2" size={48} />
                      <p className="text-muted">No shippers found</p>
                    </div>
                  }
                  progressPending={false}
                  progressComponent={
                    <div className="text-center py-4">
                      <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    </div>
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Shippers;
