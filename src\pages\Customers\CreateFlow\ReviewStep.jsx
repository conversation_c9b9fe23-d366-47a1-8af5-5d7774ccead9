import React from 'react';
import { 
  FaBuilding, 
  FaUser, 
  FaMapMarkerAlt, 
  FaRupeeSign,
  FaEdit,
  FaCheckCircle,
  FaEnvelope,
  FaPhone,
  FaGlobe,
  FaIdCard,
  FaShieldAlt,
  FaCalendarAlt,
  FaFileAlt
} from 'react-icons/fa';

const ReviewStep = ({ formik }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  const getCustomerTypeLabel = (type) => {
    const types = {
      regular: '🏢 Regular Customer',
      premium: '⭐ Premium Customer',
      enterprise: '🏭 Enterprise Customer'
    };
    return types[type] || type;
  };

  return (
    <div className="review-step">
      <div className="mb-4">
        <h4 className="text-primary fw-bold mb-2">
          <FaCheckCircle className="me-2" />
          Review Customer Information
        </h4>
        <p className="text-muted">
          Please review all the information below before creating the customer profile. You can go back to edit any section if needed.
        </p>
      </div>

      <div className="row g-4">
        {/* Company Information */}
        <div className="col-lg-6">
          <div className="card border-0 shadow-sm h-100">
            <div className="card-header bg-primary bg-opacity-10 border-0">
              <div className="d-flex justify-content-between align-items-center">
                <h6 className="mb-0 fw-bold text-primary">
                  <FaBuilding className="me-2" />
                  Company Information
                </h6>
                <button className="btn btn-sm btn-outline-primary">
                  <FaEdit size={12} />
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaBuilding className="text-primary me-2" />
                    <strong>Company Name:</strong>
                  </div>
                  <div className="text-muted">{formik.values.companyName || 'Not provided'}</div>
                </div>
                
                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaUser className="text-success me-2" />
                    <strong>Customer Type:</strong>
                  </div>
                  <div className="text-muted">{getCustomerTypeLabel(formik.values.customerType)}</div>
                </div>

                {formik.values.gstNumber && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaShieldAlt className="text-warning me-2" />
                      <strong>GST Number:</strong>
                    </div>
                    <div className="text-muted">{formik.values.gstNumber}</div>
                  </div>
                )}

                {formik.values.panNumber && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaIdCard className="text-info me-2" />
                      <strong>PAN Number:</strong>
                    </div>
                    <div className="text-muted">{formik.values.panNumber}</div>
                  </div>
                )}

                {formik.values.website && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaGlobe className="text-primary me-2" />
                      <strong>Website:</strong>
                    </div>
                    <div className="text-muted">
                      <a href={formik.values.website} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                        {formik.values.website}
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="col-lg-6">
          <div className="card border-0 shadow-sm h-100">
            <div className="card-header bg-success bg-opacity-10 border-0">
              <div className="d-flex justify-content-between align-items-center">
                <h6 className="mb-0 fw-bold text-success">
                  <FaUser className="me-2" />
                  Contact Information
                </h6>
                <button className="btn btn-sm btn-outline-success">
                  <FaEdit size={12} />
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaUser className="text-primary me-2" />
                    <strong>Contact Person:</strong>
                  </div>
                  <div className="text-muted">{formik.values.contactPerson || 'Not provided'}</div>
                </div>

                {formik.values.designation && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaUser className="text-success me-2" />
                      <strong>Designation:</strong>
                    </div>
                    <div className="text-muted">{formik.values.designation}</div>
                  </div>
                )}

                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaEnvelope className="text-info me-2" />
                    <strong>Email:</strong>
                  </div>
                  <div className="text-muted">{formik.values.email || 'Not provided'}</div>
                </div>

                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaPhone className="text-warning me-2" />
                    <strong>Phone:</strong>
                  </div>
                  <div className="text-muted">{formik.values.phone || 'Not provided'}</div>
                </div>

                {formik.values.alternatePhone && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaPhone className="text-secondary me-2" />
                      <strong>Alternate Phone:</strong>
                    </div>
                    <div className="text-muted">{formik.values.alternatePhone}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Address Information */}
        <div className="col-lg-6">
          <div className="card border-0 shadow-sm h-100">
            <div className="card-header bg-warning bg-opacity-10 border-0">
              <div className="d-flex justify-content-between align-items-center">
                <h6 className="mb-0 fw-bold text-warning">
                  <FaMapMarkerAlt className="me-2" />
                  Address Information
                </h6>
                <button className="btn btn-sm btn-outline-warning">
                  <FaEdit size={12} />
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-12">
                  <div className="d-flex align-items-start mb-2">
                    <FaMapMarkerAlt className="text-danger me-2 mt-1" />
                    <strong>Address:</strong>
                  </div>
                  <div className="text-muted">{formik.values.address || 'Not provided'}</div>
                </div>

                <div className="col-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaBuilding className="text-primary me-2" />
                    <strong>City:</strong>
                  </div>
                  <div className="text-muted">{formik.values.city || 'Not provided'}</div>
                </div>

                <div className="col-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaMapMarkerAlt className="text-success me-2" />
                    <strong>State:</strong>
                  </div>
                  <div className="text-muted">{formik.values.state || 'Not provided'}</div>
                </div>

                <div className="col-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaMapMarkerAlt className="text-warning me-2" />
                    <strong>Pincode:</strong>
                  </div>
                  <div className="text-muted">{formik.values.pincode || 'Not provided'}</div>
                </div>

                <div className="col-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaGlobe className="text-info me-2" />
                    <strong>Country:</strong>
                  </div>
                  <div className="text-muted">{formik.values.country}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Business Terms */}
        <div className="col-lg-6">
          <div className="card border-0 shadow-sm h-100">
            <div className="card-header bg-info bg-opacity-10 border-0">
              <div className="d-flex justify-content-between align-items-center">
                <h6 className="mb-0 fw-bold text-info">
                  <FaRupeeSign className="me-2" />
                  Business Terms
                </h6>
                <button className="btn btn-sm btn-outline-info">
                  <FaEdit size={12} />
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaRupeeSign className="text-success me-2" />
                    <strong>Credit Limit:</strong>
                  </div>
                  <div className="text-muted">
                    {formik.values.creditLimit ? formatCurrency(formik.values.creditLimit) : 'Not provided'}
                  </div>
                </div>

                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaCalendarAlt className="text-primary me-2" />
                    <strong>Payment Terms:</strong>
                  </div>
                  <div className="text-muted">{formik.values.paymentTerms} Days</div>
                </div>

                {formik.values.notes && (
                  <div className="col-12">
                    <div className="d-flex align-items-start mb-2">
                      <FaFileAlt className="text-info me-2 mt-1" />
                      <strong>Notes:</strong>
                    </div>
                    <div className="text-muted">{formik.values.notes}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Message */}
      <div className="mt-4">
        <div className="alert alert-primary border-0" role="alert">
          <div className="d-flex align-items-center">
            <FaCheckCircle className="text-primary me-3" size={24} />
            <div>
              <h6 className="alert-heading mb-1">Ready to Create Customer</h6>
              <p className="mb-0">
                All information has been reviewed. Click "Confirm & Submit" to create the customer profile.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewStep;
