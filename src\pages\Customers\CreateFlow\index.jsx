import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import toast from 'react-hot-toast';
import MultiStepFormWrapper from '../../../components/MultiStepForm/MultiStepFormWrapper';
import CompanyDetailsStep from './CompanyDetailsStep';
import ContactDetailsStep from './ContactDetailsStep';
import AddressBusinessStep from './AddressBusinessStep';
import ReviewStep from './ReviewStep';
import ConfirmationStep from './ConfirmationStep';
import ROUTES from '@constants/routes';

const CustomerCreateFlow = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const steps = [
    {
      title: 'Company Details',
      description: 'Basic company information',
      component: CompanyDetailsStep
    },
    {
      title: 'Contact Information',
      description: 'Contact person details',
      component: ContactDetailsStep
    },
    {
      title: 'Address & Business',
      description: 'Location and terms',
      component: AddressBusinessStep
    },
    {
      title: 'Review & Confirm',
      description: 'Verify all information',
      component: ReviewStep
    },
    {
      title: 'Confirmation',
      description: 'Success confirmation',
      component: ConfirmationStep
    }
  ];

  const validationSchemas = [
    // Step 1: Company Details
    Yup.object({
      companyName: Yup.string().required('Company name is required'),
      customerType: Yup.string().required('Customer type is required'),
      gstNumber: Yup.string().matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Invalid GST number format'),
      panNumber: Yup.string().matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Invalid PAN number format'),
      website: Yup.string().url('Invalid website URL')
    }),
    // Step 2: Contact Information
    Yup.object({
      contactPerson: Yup.string().required('Contact person is required'),
      email: Yup.string().email('Invalid email address').required('Email is required'),
      phone: Yup.string().required('Phone number is required'),
      alternatePhone: Yup.string()
    }),
    // Step 3: Address & Business
    Yup.object({
      address: Yup.string().required('Address is required'),
      city: Yup.string().required('City is required'),
      state: Yup.string().required('State is required'),
      pincode: Yup.string().required('Pincode is required').matches(/^[0-9]{6}$/, 'Invalid pincode'),
      creditLimit: Yup.number().required('Credit limit is required').min(0, 'Credit limit must be positive'),
      paymentTerms: Yup.string().required('Payment terms are required')
    })
  ];

  const formik = useFormik({
    initialValues: {
      // Company Details
      companyName: '',
      customerType: 'regular',
      gstNumber: '',
      panNumber: '',
      website: '',
      
      // Contact Information
      contactPerson: '',
      designation: '',
      email: '',
      phone: '',
      alternatePhone: '',
      
      // Address & Business
      address: '',
      city: '',
      state: '',
      pincode: '',
      country: 'India',
      creditLimit: '',
      paymentTerms: '30',
      notes: ''
    },
    validationSchema: validationSchemas[currentStep] || Yup.object(),
    onSubmit: async (values) => {
      if (currentStep < 3) {
        handleNext();
      } else {
        await handleFinalSubmit(values);
      }
    }
  });

  const handleNext = async () => {
    const isValid = await formik.validateForm();
    const hasErrors = Object.keys(isValid).length > 0;
    
    if (!hasErrors) {
      setCompletedSteps(prev => [...prev, currentStep]);
      setCurrentStep(prev => prev + 1);
      
      // Update validation schema for next step
      if (currentStep + 1 < validationSchemas.length) {
        formik.setValidationSchema(validationSchemas[currentStep + 1]);
      }
    } else {
      formik.setTouched(
        Object.keys(formik.values).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
    if (currentStep - 1 >= 0 && currentStep - 1 < validationSchemas.length) {
      formik.setValidationSchema(validationSchemas[currentStep - 1]);
    }
  };

  const handleFinalSubmit = async (values) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Customer created:', values);
      toast.success('Customer created successfully!');
      
      setCurrentStep(4); // Move to confirmation step
    } catch (error) {
      toast.error('Failed to create customer. Please try again.');
      console.error('Error creating customer:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const canProceed = () => {
    if (currentStep >= validationSchemas.length) return true;
    
    const schema = validationSchemas[currentStep];
    try {
      schema.validateSync(formik.values, { abortEarly: false });
      return true;
    } catch {
      return false;
    }
  };

  const CurrentStepComponent = steps[currentStep]?.component;

  return (
    <MultiStepFormWrapper
      steps={steps}
      currentStep={currentStep}
      onNext={handleNext}
      onPrevious={handlePrevious}
      onSubmit={() => handleFinalSubmit(formik.values)}
      isSubmitting={isSubmitting}
      variant="customer"
      backUrl={ROUTES.CUSTOMERS}
      title="Create New Customer"
      subtitle="Add a comprehensive customer profile for seamless logistics management"
      completedSteps={completedSteps}
      canProceed={canProceed()}
      showReview={true}
    >
      {CurrentStepComponent && (
        <CurrentStepComponent 
          formik={formik}
          onNext={handleNext}
          isLastStep={currentStep === steps.length - 1}
        />
      )}
    </MultiStepFormWrapper>
  );
};

export default CustomerCreateFlow;
