import React from 'react';
import { 
  <PERSON><PERSON><PERSON><PERSON>, 
  Fa<PERSON>ser<PERSON><PERSON>, 
  FaEnvelope, 
  FaPhone,
  FaInfoCircle,
  FaAddressCard
} from 'react-icons/fa';

const ContactDetailsStep = ({ formik }) => {
  return (
    <div className="contact-details-step">
      <div className="row">
        <div className="col-lg-8">
          <div className="mb-4">
            <h4 className="text-primary fw-bold mb-2">
              <FaUser className="me-2" />
              Contact Information
            </h4>
            <p className="text-muted">
              Provide the primary contact person details. This person will be the main point of communication for all business activities.
            </p>
          </div>

          <div className="row g-4">
            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="text"
                  className={`form-control ${formik.touched.contactPerson && formik.errors.contactPerson ? 'is-invalid' : ''}`}
                  id="contact<PERSON>erson"
                  name="contact<PERSON><PERSON>"
                  value={formik.values.contactPerson}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="Enter contact person name"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="contactPerson" className="fw-semibold">
                  <FaUser className="me-2 text-primary" />
                  Contact Person *
                </label>
                {formik.touched.contactPerson && formik.errors.contactPerson && (
                  <div className="invalid-feedback">{formik.errors.contactPerson}</div>
                )}
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="text"
                  className="form-control"
                  id="designation"
                  name="designation"
                  value={formik.values.designation}
                  onChange={formik.handleChange}
                  placeholder="Manager, Director, etc."
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="designation" className="fw-semibold">
                  <FaUserTie className="me-2 text-success" />
                  Designation (Optional)
                </label>
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="email"
                  className={`form-control ${formik.touched.email && formik.errors.email ? 'is-invalid' : ''}`}
                  id="email"
                  name="email"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="<EMAIL>"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="email" className="fw-semibold">
                  <FaEnvelope className="me-2 text-info" />
                  Email Address *
                </label>
                {formik.touched.email && formik.errors.email && (
                  <div className="invalid-feedback">{formik.errors.email}</div>
                )}
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="tel"
                  className={`form-control ${formik.touched.phone && formik.errors.phone ? 'is-invalid' : ''}`}
                  id="phone"
                  name="phone"
                  value={formik.values.phone}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="+91 98765 43210"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="phone" className="fw-semibold">
                  <FaPhone className="me-2 text-warning" />
                  Phone Number *
                </label>
                {formik.touched.phone && formik.errors.phone && (
                  <div className="invalid-feedback">{formik.errors.phone}</div>
                )}
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="tel"
                  className={`form-control ${formik.touched.alternatePhone && formik.errors.alternatePhone ? 'is-invalid' : ''}`}
                  id="alternatePhone"
                  name="alternatePhone"
                  value={formik.values.alternatePhone}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="+91 87654 32109"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="alternatePhone" className="fw-semibold">
                  <FaPhone className="me-2 text-secondary" />
                  Alternate Phone (Optional)
                </label>
                {formik.touched.alternatePhone && formik.errors.alternatePhone && (
                  <div className="invalid-feedback">{formik.errors.alternatePhone}</div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-4">
          {/* Help Section */}
          <div className="card border-0 bg-light">
            <div className="card-body p-4">
              <h6 className="fw-bold text-primary mb-3">
                <FaInfoCircle className="me-2" />
                Contact Guidelines
              </h6>
              <ul className="list-unstyled mb-0 small">
                <li className="mb-2">
                  <strong>Primary Contact:</strong> Should be someone authorized to make business decisions.
                </li>
                <li className="mb-2">
                  <strong>Email:</strong> Will be used for invoices, notifications, and important communications.
                </li>
                <li className="mb-2">
                  <strong>Phone:</strong> Primary number for urgent communications and order confirmations.
                </li>
                <li className="mb-2">
                  <strong>Alternate Phone:</strong> Backup contact for emergencies or when primary is unavailable.
                </li>
              </ul>
            </div>
          </div>

          {/* Contact Best Practices */}
          <div className="card border-0 bg-success bg-opacity-10 mt-3">
            <div className="card-body p-4">
              <h6 className="fw-bold text-success mb-3">
                <FaAddressCard className="me-2" />
                Best Practices
              </h6>
              <div className="small">
                <div className="mb-2">
                  ✅ Verify email address for accuracy
                </div>
                <div className="mb-2">
                  ✅ Include country code for phone numbers
                </div>
                <div className="mb-2">
                  ✅ Ensure contact person has decision-making authority
                </div>
                <div className="mb-2">
                  ✅ Keep contact information updated
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactDetailsStep;
