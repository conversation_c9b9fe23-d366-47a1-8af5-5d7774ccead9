import React, { useState } from "react";
import { Outlet, useNavigate } from "react-router-dom";
import { useDispatch } from "react-redux";
import { resetUser } from "@store/userSlice";
import toast from "react-hot-toast";

// Import separated components
import Sidebar from "./Components/Sidebar";
import TopHeader from "./Components/TopHeader";
import ROUTES from "@constants/routes";

const MainLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [toggled, setToggled] = useState(false);
  const navigate = useNavigate();
  const dispatch = useDispatch();

  const handleLogout = () => {
    dispatch(resetUser());
    toast.success("Logged out successfully");
    navigate(ROUTES.LOGIN);
  };

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const toggleMobileSidebar = () => {
    setToggled(!toggled);
  };

  const handleSearch = (searchTerm) => {
    console.log("Search:", searchTerm);
    // Implement search functionality here
  };

  return (
    <div className="main-layout d-flex vh-100">
      {/* Professional Sidebar */}
      <Sidebar
        collapsed={collapsed}
        toggled={toggled}
        onBackdropClick={() => setToggled(false)}
      />

      {/* Main Content Area */}
      <div className="main-content flex-grow-1 d-flex flex-column overflow-hidden">
        {/* Top Header Bar */}
        <TopHeader
          collapsed={collapsed}
          onToggleSidebar={toggleSidebar}
          onToggleMobileSidebar={toggleMobileSidebar}
          onLogout={handleLogout}
        />

        {/* Page Content */}
        <main className="page-content flex-grow-1 overflow-auto p-4">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default MainLayout;
