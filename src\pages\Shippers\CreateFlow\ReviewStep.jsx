import React from 'react';
import { 
  FaTruck, 
  FaUser, 
  FaMapMarkerAlt, 
  FaRoute,
  FaEdit,
  FaCheckCircle,
  FaEnvelope,
  FaPhone,
  FaGlobe,
  FaIdCard,
  FaShieldAlt,
  FaCertificate,
  FaRupeeSign,
  FaCogs,
  FaFileAlt,
  FaShippingFast
} from 'react-icons/fa';

const ReviewStep = ({ formik }) => {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 2
    }).format(amount);
  };

  const getServiceTypeLabel = (type) => {
    const types = {
      standard: '🚛 Standard Delivery',
      express: '⚡ Express Delivery',
      heavy: '🏗️ Heavy Cargo',
      refrigerated: '❄️ Refrigerated Transport',
      hazardous: '⚠️ Hazardous Materials'
    };
    return types[type] || type;
  };

  return (
    <div className="review-step">
      <div className="mb-4">
        <h4 className="text-success fw-bold mb-2">
          <FaCheckCircle className="me-2" />
          Review Shipping Partner Information
        </h4>
        <p className="text-muted">
          Please review all the information below before creating the shipping partner profile. You can go back to edit any section if needed.
        </p>
      </div>

      <div className="row g-4">
        {/* Company Information */}
        <div className="col-lg-6">
          <div className="card border-0 shadow-sm h-100">
            <div className="card-header bg-success bg-opacity-10 border-0">
              <div className="d-flex justify-content-between align-items-center">
                <h6 className="mb-0 fw-bold text-success">
                  <FaTruck className="me-2" />
                  Company Information
                </h6>
                <button className="btn btn-sm btn-outline-success">
                  <FaEdit size={12} />
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaTruck className="text-success me-2" />
                    <strong>Company Name:</strong>
                  </div>
                  <div className="text-muted">{formik.values.companyName || 'Not provided'}</div>
                </div>
                
                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaRoute className="text-primary me-2" />
                    <strong>Service Type:</strong>
                  </div>
                  <div className="text-muted">{getServiceTypeLabel(formik.values.serviceType)}</div>
                </div>

                {formik.values.gstNumber && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaShieldAlt className="text-warning me-2" />
                      <strong>GST Number:</strong>
                    </div>
                    <div className="text-muted">{formik.values.gstNumber}</div>
                  </div>
                )}

                {formik.values.panNumber && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaIdCard className="text-info me-2" />
                      <strong>PAN Number:</strong>
                    </div>
                    <div className="text-muted">{formik.values.panNumber}</div>
                  </div>
                )}

                {formik.values.licenseNumber && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaCertificate className="text-warning me-2" />
                      <strong>Transport License:</strong>
                    </div>
                    <div className="text-muted">{formik.values.licenseNumber}</div>
                  </div>
                )}

                {formik.values.insuranceNumber && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaShieldAlt className="text-danger me-2" />
                      <strong>Insurance Policy:</strong>
                    </div>
                    <div className="text-muted">{formik.values.insuranceNumber}</div>
                  </div>
                )}

                {formik.values.website && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaGlobe className="text-primary me-2" />
                      <strong>Website:</strong>
                    </div>
                    <div className="text-muted">
                      <a href={formik.values.website} target="_blank" rel="noopener noreferrer" className="text-decoration-none">
                        {formik.values.website}
                      </a>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Contact Information */}
        <div className="col-lg-6">
          <div className="card border-0 shadow-sm h-100">
            <div className="card-header bg-primary bg-opacity-10 border-0">
              <div className="d-flex justify-content-between align-items-center">
                <h6 className="mb-0 fw-bold text-primary">
                  <FaUser className="me-2" />
                  Contact Information
                </h6>
                <button className="btn btn-sm btn-outline-primary">
                  <FaEdit size={12} />
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaUser className="text-primary me-2" />
                    <strong>Contact Person:</strong>
                  </div>
                  <div className="text-muted">{formik.values.contactPerson || 'Not provided'}</div>
                </div>

                {formik.values.designation && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaUser className="text-success me-2" />
                      <strong>Designation:</strong>
                    </div>
                    <div className="text-muted">{formik.values.designation}</div>
                  </div>
                )}

                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaEnvelope className="text-info me-2" />
                    <strong>Email:</strong>
                  </div>
                  <div className="text-muted">{formik.values.email || 'Not provided'}</div>
                </div>

                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaPhone className="text-warning me-2" />
                    <strong>Phone:</strong>
                  </div>
                  <div className="text-muted">{formik.values.phone || 'Not provided'}</div>
                </div>

                {formik.values.alternatePhone && (
                  <div className="col-12">
                    <div className="d-flex align-items-center mb-2">
                      <FaPhone className="text-secondary me-2" />
                      <strong>Alternate Phone:</strong>
                    </div>
                    <div className="text-muted">{formik.values.alternatePhone}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Fleet Information */}
        <div className="col-lg-6">
          <div className="card border-0 shadow-sm h-100">
            <div className="card-header bg-warning bg-opacity-10 border-0">
              <div className="d-flex justify-content-between align-items-center">
                <h6 className="mb-0 fw-bold text-warning">
                  <FaTruck className="me-2" />
                  Fleet Information
                </h6>
                <button className="btn btn-sm btn-outline-warning">
                  <FaEdit size={12} />
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaTruck className="text-primary me-2" />
                    <strong>Vehicle Count:</strong>
                  </div>
                  <div className="text-muted">{formik.values.vehicleCount || 'Not provided'} vehicles</div>
                </div>

                <div className="col-12">
                  <div className="d-flex align-items-center mb-2">
                    <FaRupeeSign className="text-success me-2" />
                    <strong>Rate per KM:</strong>
                  </div>
                  <div className="text-muted">
                    {formik.values.ratePerKm ? formatCurrency(formik.values.ratePerKm) + ' per KM' : 'Not provided'}
                  </div>
                </div>

                {formik.values.vehicleTypes.length > 0 && (
                  <div className="col-12">
                    <div className="d-flex align-items-start mb-2">
                      <FaCogs className="text-info me-2 mt-1" />
                      <strong>Vehicle Types:</strong>
                    </div>
                    <div className="text-muted">
                      {formik.values.vehicleTypes.map(type => (
                        <span key={type} className="badge bg-info bg-opacity-20 text-info me-1 mb-1">
                          {type}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Address & Operations */}
        <div className="col-lg-6">
          <div className="card border-0 shadow-sm h-100">
            <div className="card-header bg-info bg-opacity-10 border-0">
              <div className="d-flex justify-content-between align-items-center">
                <h6 className="mb-0 fw-bold text-info">
                  <FaMapMarkerAlt className="me-2" />
                  Address & Operations
                </h6>
                <button className="btn btn-sm btn-outline-info">
                  <FaEdit size={12} />
                </button>
              </div>
            </div>
            <div className="card-body">
              <div className="row g-3">
                <div className="col-12">
                  <div className="d-flex align-items-start mb-2">
                    <FaMapMarkerAlt className="text-danger me-2 mt-1" />
                    <strong>Address:</strong>
                  </div>
                  <div className="text-muted">{formik.values.address || 'Not provided'}</div>
                </div>

                <div className="col-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaBuilding className="text-primary me-2" />
                    <strong>City:</strong>
                  </div>
                  <div className="text-muted">{formik.values.city || 'Not provided'}</div>
                </div>

                <div className="col-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaMapMarkerAlt className="text-success me-2" />
                    <strong>State:</strong>
                  </div>
                  <div className="text-muted">{formik.values.state || 'Not provided'}</div>
                </div>

                <div className="col-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaMapMarkerAlt className="text-warning me-2" />
                    <strong>Pincode:</strong>
                  </div>
                  <div className="text-muted">{formik.values.pincode || 'Not provided'}</div>
                </div>

                {formik.values.operatingStates.length > 0 && (
                  <div className="col-12">
                    <div className="d-flex align-items-start mb-2">
                      <FaRoute className="text-success me-2 mt-1" />
                      <strong>Operating States ({formik.values.operatingStates.length}):</strong>
                    </div>
                    <div className="text-muted">
                      {formik.values.operatingStates.slice(0, 5).map(state => (
                        <span key={state} className="badge bg-success bg-opacity-20 text-success me-1 mb-1 small">
                          {state}
                        </span>
                      ))}
                      {formik.values.operatingStates.length > 5 && (
                        <span className="badge bg-success bg-opacity-20 text-success small">
                          +{formik.values.operatingStates.length - 5} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {formik.values.notes && (
                  <div className="col-12">
                    <div className="d-flex align-items-start mb-2">
                      <FaFileAlt className="text-info me-2 mt-1" />
                      <strong>Notes:</strong>
                    </div>
                    <div className="text-muted">{formik.values.notes}</div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Confirmation Message */}
      <div className="mt-4">
        <div className="alert alert-success border-0" role="alert">
          <div className="d-flex align-items-center">
            <FaCheckCircle className="text-success me-3" size={24} />
            <div>
              <h6 className="alert-heading mb-1">Ready to Create Shipping Partner</h6>
              <p className="mb-0">
                All information has been reviewed. Click "Confirm & Submit" to create the shipping partner profile.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReviewStep;
