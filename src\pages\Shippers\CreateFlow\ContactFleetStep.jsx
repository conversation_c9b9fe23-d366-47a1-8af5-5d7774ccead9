import React from 'react';
import { 
  <PERSON>a<PERSON><PERSON>, 
  FaUser<PERSON>ie, 
  FaEnvelope, 
  FaPhone,
  FaTruck,
  FaRupeeSign,
  FaCogs,
  FaInfoCircle,
  FaShippingFast
} from 'react-icons/fa';

const ContactFleetStep = ({ formik, onVehicleTypeChange }) => {
  return (
    <div className="contact-fleet-step">
      <div className="row">
        <div className="col-lg-8">
          <div className="mb-4">
            <h4 className="text-success fw-bold mb-2">
              <FaUser className="me-2" />
              Contact & Fleet Information
            </h4>
            <p className="text-muted">
              Provide contact details and fleet information for logistics coordination and capacity planning.
            </p>
          </div>

          {/* Contact Information Section */}
          <div className="mb-5">
            <h5 className="text-secondary fw-semibold mb-3">
              <FaUser className="me-2" />
              Contact Details
            </h5>
            <div className="row g-4">
              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="text"
                    className={`form-control ${formik.touched.contactPerson && formik.errors.contactPerson ? 'is-invalid' : ''}`}
                    id="contactPerson"
                    name="contactPerson"
                    value={formik.values.contactPerson}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter contact person name"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="contactPerson" className="fw-semibold">
                    <FaUser className="me-2 text-primary" />
                    Contact Person *
                  </label>
                  {formik.touched.contactPerson && formik.errors.contactPerson && (
                    <div className="invalid-feedback">{formik.errors.contactPerson}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="text"
                    className="form-control"
                    id="designation"
                    name="designation"
                    value={formik.values.designation}
                    onChange={formik.handleChange}
                    placeholder="Manager, Director, etc."
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="designation" className="fw-semibold">
                    <FaUserTie className="me-2 text-success" />
                    Designation (Optional)
                  </label>
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="email"
                    className={`form-control ${formik.touched.email && formik.errors.email ? 'is-invalid' : ''}`}
                    id="email"
                    name="email"
                    value={formik.values.email}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="<EMAIL>"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="email" className="fw-semibold">
                    <FaEnvelope className="me-2 text-info" />
                    Email Address *
                  </label>
                  {formik.touched.email && formik.errors.email && (
                    <div className="invalid-feedback">{formik.errors.email}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="tel"
                    className={`form-control ${formik.touched.phone && formik.errors.phone ? 'is-invalid' : ''}`}
                    id="phone"
                    name="phone"
                    value={formik.values.phone}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="+91 98765 43210"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="phone" className="fw-semibold">
                    <FaPhone className="me-2 text-warning" />
                    Phone Number *
                  </label>
                  {formik.touched.phone && formik.errors.phone && (
                    <div className="invalid-feedback">{formik.errors.phone}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="tel"
                    className="form-control"
                    id="alternatePhone"
                    name="alternatePhone"
                    value={formik.values.alternatePhone}
                    onChange={formik.handleChange}
                    placeholder="+91 87654 32109"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="alternatePhone" className="fw-semibold">
                    <FaPhone className="me-2 text-secondary" />
                    Alternate Phone (Optional)
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Fleet Information Section */}
          <div className="mb-4">
            <h5 className="text-secondary fw-semibold mb-3">
              <FaTruck className="me-2" />
              Fleet & Pricing Information
            </h5>
            <div className="row g-4">
              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="number"
                    className={`form-control ${formik.touched.vehicleCount && formik.errors.vehicleCount ? 'is-invalid' : ''}`}
                    id="vehicleCount"
                    name="vehicleCount"
                    value={formik.values.vehicleCount}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="25"
                    min="1"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="vehicleCount" className="fw-semibold">
                    <FaTruck className="me-2 text-primary" />
                    Total Vehicle Count *
                  </label>
                  {formik.touched.vehicleCount && formik.errors.vehicleCount && (
                    <div className="invalid-feedback">{formik.errors.vehicleCount}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="number"
                    className={`form-control ${formik.touched.ratePerKm && formik.errors.ratePerKm ? 'is-invalid' : ''}`}
                    id="ratePerKm"
                    name="ratePerKm"
                    value={formik.values.ratePerKm}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="15.50"
                    step="0.01"
                    min="0"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="ratePerKm" className="fw-semibold">
                    <FaRupeeSign className="me-2 text-success" />
                    Rate per KM (₹) *
                  </label>
                  {formik.touched.ratePerKm && formik.errors.ratePerKm && (
                    <div className="invalid-feedback">{formik.errors.ratePerKm}</div>
                  )}
                </div>
              </div>

              <div className="col-12">
                <label className="form-label fw-semibold mb-3">
                  <FaCogs className="me-2 text-info" />
                  Available Vehicle Types *
                </label>
                <div className="row g-3">
                  {[
                    { type: 'Mini Truck', icon: '🚐' },
                    { type: 'Small Truck', icon: '🚚' },
                    { type: 'Medium Truck', icon: '🚛' },
                    { type: 'Large Truck', icon: '🚜' },
                    { type: 'Container', icon: '📦' },
                    { type: 'Trailer', icon: '🚛' },
                    { type: 'Refrigerated', icon: '❄️' },
                    { type: 'Tanker', icon: '🛢️' }
                  ].map(({ type, icon }) => (
                    <div key={type} className="col-md-3">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id={`vehicle-${type}`}
                          checked={formik.values.vehicleTypes.includes(type)}
                          onChange={() => onVehicleTypeChange(type)}
                          style={{ transform: 'scale(1.2)' }}
                        />
                        <label className="form-check-label fw-semibold" htmlFor={`vehicle-${type}`}>
                          <span className="me-2">{icon}</span>
                          {type}
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
                {formik.touched.vehicleTypes && formik.errors.vehicleTypes && (
                  <div className="text-danger small mt-2">{formik.errors.vehicleTypes}</div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-4">
          {/* Contact Help */}
          <div className="card border-0 bg-light mb-3">
            <div className="card-body p-4">
              <h6 className="fw-bold text-success mb-3">
                <FaInfoCircle className="me-2" />
                Contact Guidelines
              </h6>
              <ul className="list-unstyled mb-0 small">
                <li className="mb-2">
                  <strong>Primary Contact:</strong> Should be available for logistics coordination and emergency situations.
                </li>
                <li className="mb-2">
                  <strong>Email:</strong> Will receive shipment notifications, invoices, and important updates.
                </li>
                <li className="mb-2">
                  <strong>Phone:</strong> Primary number for real-time coordination and tracking updates.
                </li>
              </ul>
            </div>
          </div>

          {/* Fleet Help */}
          <div className="card border-0 bg-success bg-opacity-10">
            <div className="card-body p-4">
              <h6 className="fw-bold text-success mb-3">
                <FaShippingFast className="me-2" />
                Fleet Information
              </h6>
              <div className="small">
                <div className="mb-2">
                  <strong>Vehicle Count:</strong> Total number of vehicles in your active fleet.
                </div>
                <div className="mb-2">
                  <strong>Rate per KM:</strong> Your standard rate for distance-based pricing.
                </div>
                <div className="mb-2">
                  <strong>Vehicle Types:</strong> Select all types available in your fleet for better matching.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ContactFleetStep;
