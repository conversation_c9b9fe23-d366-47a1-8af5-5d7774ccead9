import React from 'react';
import { 
  FaMapMarkerAlt, 
  FaBuilding, 
  FaGlobe, 
  FaRoute,
  FaFileAlt,
  FaInfoCircle,
  FaShippingFast
} from 'react-icons/fa';

const AddressOperationsStep = ({ formik, onOperatingStateChange }) => {
  const indianStates = [
    'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh',
    'Goa', 'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka',
    'Kerala', 'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram',
    'Nagaland', 'Odisha', 'Punjab', 'Rajasthan', 'Sikkim', 'Tamil Nadu',
    'Telangana', 'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal',
    'Delhi', 'Jammu and Kashmir', 'Ladakh', 'Chandigarh', 'Dadra and Nagar Haveli',
    'Daman and Diu', 'Lakshadweep', 'Puducherry'
  ];

  return (
    <div className="address-operations-step">
      <div className="row">
        <div className="col-lg-8">
          <div className="mb-4">
            <h4 className="text-success fw-bold mb-2">
              <FaMapMarkerAlt className="me-2" />
              Address & Operations
            </h4>
            <p className="text-muted">
              Complete the shipping partner profile with address details and operational coverage areas.
            </p>
          </div>

          {/* Address Section */}
          <div className="mb-5">
            <h5 className="text-secondary fw-semibold mb-3">
              <FaBuilding className="me-2" />
              Business Address
            </h5>
            <div className="row g-4">
              <div className="col-12">
                <div className="form-floating">
                  <textarea
                    className={`form-control ${formik.touched.address && formik.errors.address ? 'is-invalid' : ''}`}
                    id="address"
                    name="address"
                    rows="3"
                    value={formik.values.address}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter complete address"
                    style={{ borderRadius: '10px', minHeight: '100px' }}
                  />
                  <label htmlFor="address" className="fw-semibold">
                    <FaMapMarkerAlt className="me-2 text-danger" />
                    Complete Address *
                  </label>
                  {formik.touched.address && formik.errors.address && (
                    <div className="invalid-feedback">{formik.errors.address}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="text"
                    className={`form-control ${formik.touched.city && formik.errors.city ? 'is-invalid' : ''}`}
                    id="city"
                    name="city"
                    value={formik.values.city}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter city"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="city" className="fw-semibold">
                    <FaBuilding className="me-2 text-primary" />
                    City *
                  </label>
                  {formik.touched.city && formik.errors.city && (
                    <div className="invalid-feedback">{formik.errors.city}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <select
                    className={`form-select ${formik.touched.state && formik.errors.state ? 'is-invalid' : ''}`}
                    id="state"
                    name="state"
                    value={formik.values.state}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    style={{ borderRadius: '10px' }}
                  >
                    <option value="">Select State</option>
                    {indianStates.map(state => (
                      <option key={state} value={state}>{state}</option>
                    ))}
                  </select>
                  <label htmlFor="state" className="fw-semibold">
                    <FaMapMarkerAlt className="me-2 text-success" />
                    State *
                  </label>
                  {formik.touched.state && formik.errors.state && (
                    <div className="invalid-feedback">{formik.errors.state}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="text"
                    className={`form-control ${formik.touched.pincode && formik.errors.pincode ? 'is-invalid' : ''}`}
                    id="pincode"
                    name="pincode"
                    value={formik.values.pincode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="400001"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="pincode" className="fw-semibold">
                    <FaMapMarkerAlt className="me-2 text-warning" />
                    Pincode *
                  </label>
                  {formik.touched.pincode && formik.errors.pincode && (
                    <div className="invalid-feedback">{formik.errors.pincode}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="text"
                    className="form-control"
                    id="country"
                    name="country"
                    value={formik.values.country}
                    onChange={formik.handleChange}
                    readOnly
                    style={{ borderRadius: '10px', backgroundColor: '#f8f9fa' }}
                  />
                  <label htmlFor="country" className="fw-semibold">
                    <FaGlobe className="me-2 text-info" />
                    Country
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Operating States Section */}
          <div className="mb-4">
            <h5 className="text-secondary fw-semibold mb-3">
              <FaRoute className="me-2" />
              Operating Coverage
            </h5>
            <div className="row g-3">
              <div className="col-12">
                <label className="form-label fw-semibold mb-3">
                  <FaShippingFast className="me-2 text-success" />
                  Select Operating States *
                </label>
                <div className="row g-2" style={{ maxHeight: '300px', overflowY: 'auto' }}>
                  {indianStates.map((state) => (
                    <div key={state} className="col-md-4 col-sm-6">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id={`operating-${state}`}
                          checked={formik.values.operatingStates.includes(state)}
                          onChange={() => onOperatingStateChange(state)}
                          style={{ transform: 'scale(1.1)' }}
                        />
                        <label className="form-check-label small fw-semibold" htmlFor={`operating-${state}`}>
                          {state}
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
                {formik.touched.operatingStates && formik.errors.operatingStates && (
                  <div className="text-danger small mt-2">{formik.errors.operatingStates}</div>
                )}
              </div>
            </div>
          </div>

          {/* Additional Notes Section */}
          <div className="mb-4">
            <h5 className="text-secondary fw-semibold mb-3">
              <FaFileAlt className="me-2" />
              Additional Information
            </h5>
            <div className="row g-4">
              <div className="col-12">
                <div className="form-floating">
                  <textarea
                    className="form-control"
                    id="notes"
                    name="notes"
                    rows="4"
                    value={formik.values.notes}
                    onChange={formik.handleChange}
                    placeholder="Special handling capabilities, equipment details, service limitations, etc."
                    style={{ borderRadius: '10px', minHeight: '120px' }}
                  />
                  <label htmlFor="notes" className="fw-semibold">
                    <FaFileAlt className="me-2 text-info" />
                    Service Notes & Capabilities (Optional)
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-4">
          {/* Address Help */}
          <div className="card border-0 bg-light mb-3">
            <div className="card-body p-4">
              <h6 className="fw-bold text-success mb-3">
                <FaInfoCircle className="me-2" />
                Address Guidelines
              </h6>
              <ul className="list-unstyled mb-0 small">
                <li className="mb-2">
                  <strong>Complete Address:</strong> Include depot/warehouse location with landmarks for easy identification.
                </li>
                <li className="mb-2">
                  <strong>Pincode:</strong> Essential for route optimization and delivery planning.
                </li>
                <li className="mb-2">
                  <strong>State:</strong> Required for interstate permit and tax compliance.
                </li>
              </ul>
            </div>
          </div>

          {/* Operating States Help */}
          <div className="card border-0 bg-success bg-opacity-10 mb-3">
            <div className="card-body p-4">
              <h6 className="fw-bold text-success mb-3">
                <FaRoute className="me-2" />
                Operating Coverage
              </h6>
              <div className="small">
                <div className="mb-2">
                  <strong>Service States:</strong> Select all states where you provide logistics services.
                </div>
                <div className="mb-2">
                  <strong>Permits:</strong> Ensure you have valid permits for interstate operations.
                </div>
                <div className="mb-2">
                  <strong>Coverage:</strong> More states = better matching opportunities.
                </div>
              </div>
            </div>
          </div>

          {/* Quick Stats */}
          <div className="card border-0 bg-info bg-opacity-10">
            <div className="card-body p-4">
              <h6 className="fw-bold text-info mb-3">
                <FaShippingFast className="me-2" />
                Coverage Summary
              </h6>
              <div className="small">
                <div className="mb-2">
                  <strong>Selected States:</strong> {formik.values.operatingStates.length} of {indianStates.length}
                </div>
                <div className="mb-2">
                  <strong>Coverage:</strong> {Math.round((formik.values.operatingStates.length / indianStates.length) * 100)}% of India
                </div>
                {formik.values.operatingStates.length > 0 && (
                  <div className="mt-2">
                    <strong>Operating in:</strong>
                    <div className="mt-1">
                      {formik.values.operatingStates.slice(0, 3).map(state => (
                        <span key={state} className="badge bg-info bg-opacity-20 text-info me-1 mb-1 small">
                          {state}
                        </span>
                      ))}
                      {formik.values.operatingStates.length > 3 && (
                        <span className="badge bg-info bg-opacity-20 text-info small">
                          +{formik.values.operatingStates.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressOperationsStep;
