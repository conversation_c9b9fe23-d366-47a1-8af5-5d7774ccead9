import React from 'react';
import { Link } from 'react-router-dom';
import { 
  FaCheckCircle, 
  FaUser, 
  FaTruck, 
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaPlus,
  FaEye,
  FaDownload,
  FaShare,
  FaRoute,
  FaShippingFast
} from 'react-icons/fa';
import ROUTES from '@constants/routes';

const ConfirmationStep = ({ formik }) => {
  return (
    <div className="confirmation-step text-center">
      {/* Success Animation */}
      <div className="mb-4">
        <div className="d-inline-flex align-items-center justify-content-center rounded-circle bg-success bg-opacity-10 mb-3" 
             style={{ width: '120px', height: '120px' }}>
          <FaCheckCircle className="text-success" size={60} />
        </div>
        <h2 className="text-success fw-bold mb-2">Shipping Partner Created Successfully!</h2>
        <p className="text-muted fs-5">
          {formik.values.companyName} has been added to your logistics network.
        </p>
      </div>

      {/* Shipper Summary Card */}
      <div className="row justify-content-center mb-4">
        <div className="col-lg-8">
          <div className="card border-0 shadow-lg">
            <div className="card-header bg-success bg-opacity-10 border-0">
              <h5 className="mb-0 fw-bold text-success text-start">
                <FaTruck className="me-2" />
                Shipping Partner Profile Created
              </h5>
            </div>
            <div className="card-body p-4">
              <div className="row g-3 text-start">
                <div className="col-md-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaTruck className="text-success me-2" />
                    <strong>Company:</strong>
                  </div>
                  <div className="text-muted">{formik.values.companyName}</div>
                </div>

                <div className="col-md-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaUser className="text-primary me-2" />
                    <strong>Contact Person:</strong>
                  </div>
                  <div className="text-muted">{formik.values.contactPerson}</div>
                </div>

                <div className="col-md-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaEnvelope className="text-info me-2" />
                    <strong>Email:</strong>
                  </div>
                  <div className="text-muted">{formik.values.email}</div>
                </div>

                <div className="col-md-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaPhone className="text-warning me-2" />
                    <strong>Phone:</strong>
                  </div>
                  <div className="text-muted">{formik.values.phone}</div>
                </div>

                <div className="col-md-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaTruck className="text-primary me-2" />
                    <strong>Fleet Size:</strong>
                  </div>
                  <div className="text-muted">{formik.values.vehicleCount} vehicles</div>
                </div>

                <div className="col-md-6">
                  <div className="d-flex align-items-center mb-2">
                    <FaRoute className="text-success me-2" />
                    <strong>Operating States:</strong>
                  </div>
                  <div className="text-muted">{formik.values.operatingStates.length} states</div>
                </div>

                <div className="col-12">
                  <div className="d-flex align-items-start mb-2">
                    <FaMapMarkerAlt className="text-danger me-2 mt-1" />
                    <strong>Address:</strong>
                  </div>
                  <div className="text-muted">
                    {formik.values.address}, {formik.values.city}, {formik.values.state} - {formik.values.pincode}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="row justify-content-center mb-4">
        <div className="col-lg-8">
          <div className="d-flex flex-wrap justify-content-center gap-3">
            <Link 
              to={ROUTES.SHIPPERS} 
              className="btn btn-success btn-lg px-4"
              style={{ borderRadius: '12px' }}
            >
              <FaEye className="me-2" />
              View All Partners
            </Link>
            
            <Link 
              to={ROUTES.SHIPPERS_CREATE_FLOW} 
              className="btn btn-primary btn-lg px-4"
              style={{ borderRadius: '12px' }}
            >
              <FaPlus className="me-2" />
              Add Another Partner
            </Link>
            
            <button 
              className="btn btn-outline-success btn-lg px-4"
              style={{ borderRadius: '12px' }}
              onClick={() => window.print()}
            >
              <FaDownload className="me-2" />
              Download Profile
            </button>
            
            <button 
              className="btn btn-outline-secondary btn-lg px-4"
              style={{ borderRadius: '12px' }}
            >
              <FaShare className="me-2" />
              Share Details
            </button>
          </div>
        </div>
      </div>

      {/* Next Steps */}
      <div className="row justify-content-center">
        <div className="col-lg-8">
          <div className="card border-0 bg-light">
            <div className="card-body p-4">
              <h6 className="fw-bold text-success mb-3 text-start">What's Next?</h6>
              <div className="row g-3 text-start">
                <div className="col-md-4">
                  <div className="d-flex align-items-start">
                    <div className="bg-success bg-opacity-10 rounded-circle p-2 me-3 mt-1">
                      <FaTruck className="text-success" size={16} />
                    </div>
                    <div>
                      <h6 className="fw-semibold mb-1">Partner Ready</h6>
                      <small className="text-muted">Shipping partner is ready for logistics operations</small>
                    </div>
                  </div>
                </div>
                
                <div className="col-md-4">
                  <div className="d-flex align-items-start">
                    <div className="bg-primary bg-opacity-10 rounded-circle p-2 me-3 mt-1">
                      <FaShippingFast className="text-primary" size={16} />
                    </div>
                    <div>
                      <h6 className="fw-semibold mb-1">Start Shipments</h6>
                      <small className="text-muted">Begin assigning shipments and managing deliveries</small>
                    </div>
                  </div>
                </div>
                
                <div className="col-md-4">
                  <div className="d-flex align-items-start">
                    <div className="bg-info bg-opacity-10 rounded-circle p-2 me-3 mt-1">
                      <FaEnvelope className="text-info" size={16} />
                    </div>
                    <div>
                      <h6 className="fw-semibold mb-1">Send Welcome</h6>
                      <small className="text-muted">Email partnership agreement and onboarding details</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Success Stats */}
      <div className="mt-4 pt-4 border-top">
        <div className="row text-center">
          <div className="col-md-4">
            <div className="text-success fw-bold h5">✅</div>
            <small className="text-muted">Partner Created</small>
          </div>
          <div className="col-md-4">
            <div className="text-primary fw-bold h5">📧</div>
            <small className="text-muted">Notification Sent</small>
          </div>
          <div className="col-md-4">
            <div className="text-info fw-bold h5">🚀</div>
            <small className="text-muted">Ready for Logistics</small>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ConfirmationStep;
