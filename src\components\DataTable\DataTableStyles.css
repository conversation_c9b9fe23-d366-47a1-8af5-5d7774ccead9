
.rdt_Table {
  border-radius: 0 !important;
}

.rdt_TableHead {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
}

.rdt_TableHeadRow {
  background-color: #f8f9fa !important;
  border-bottom: 1px solid #e9ecef !important;
  min-height: 52px !important;
}

.rdt_TableCol {
  font-weight: 600 !important;
  color: #495057 !important;
  font-size: 14px !important;
  padding: 12px 8px !important;
}

.rdt_TableRow {
  border-bottom: 1px solid #e9ecef !important;
  min-height: 60px !important;
}

.rdt_TableRow:hover {
  background-color: #f8f9fa !important;
}

.rdt_TableCell {
  padding: 12px 8px !important;
  font-size: 13px !important;
}


.rdt_Pagination {
  border-top: 1px solid #e9ecef !important;
  background-color: #ffffff !important;
  padding: 12px 16px !important;
}

.rdt_Pagination > div {
  color: #495057 !important;
  font-size: 14px !important;
}

.rdt_Pagination button {
  background-color: #ffffff !important;
  border: 1px solid #dee2e6 !important;
  color: #495057 !important;
  border-radius: 4px !important;
  padding: 6px 12px !important;
  margin: 0 2px !important;
  font-size: 14px !important;
}

.rdt_Pagination button:hover:not(:disabled) {
  background-color: #e9ecef !important;
  border-color: #adb5bd !important;
}

.rdt_Pagination button:disabled {
  background-color: #f8f9fa !important;
  border-color: #dee2e6 !important;
  color: #6c757d !important;
  cursor: not-allowed !important;
}


.rdt_Pagination select {
  border: 1px solid #dee2e6 !important;
  border-radius: 4px !important;
  padding: 4px 8px !important;
  background-color: #ffffff !important;
  color: #495057 !important;
  font-size: 14px !important;
}

.rdt_Pagination select:focus {
  border-color: #80bdff !important;
  outline: 0 !important;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25) !important;
}


.rdt_ProgressWrapper {
  background-color: #ffffff !important;
}


.rdt_TableWrapper .text-center {
  padding: 40px 20px !important;
}


@media (max-width: 768px) {
  .rdt_TableCol,
  .rdt_TableCell {
    padding: 8px 4px !important;
    font-size: 12px !important;
  }
  
  .rdt_Pagination {
    padding: 8px 12px !important;
  }
  
  .rdt_Pagination > div {
    font-size: 12px !important;
  }
  
  .rdt_Pagination button {
    padding: 4px 8px !important;
    font-size: 12px !important;
  }
}

.rdt_Checkbox {
  width: 18px !important;
  height: 18px !important;
  border: 2px solid #dee2e6 !important;
  border-radius: 3px !important;
}

.rdt_Checkbox:checked {
  background-color: #007bff !important;
  border-color: #007bff !important;
}


.rdt_TableCol svg {
  color: #6c757d !important;
}

.rdt_TableCol:hover svg {
  color: #495057 !important;
}


.rdt_TableRow:nth-child(even) {
  background-color: #f8f9fa !important;
}

.rdt_TableRow:nth-child(even):hover {
  background-color: #e9ecef !important;
}


.rdt_TableCell .btn {
  margin: 0 1px !important;
}

.rdt_TableCell .btn-sm {
  padding: 4px 8px !important;
  font-size: 11px !important;
}


.rdt_TableCell .badge {
  font-size: 11px !important;
  padding: 4px 8px !important;
}


.rdt_TableCell .text-muted {
  color: #6c757d !important;
}


.rdt_Table::-webkit-scrollbar {
  height: 8px;
}

.rdt_Table::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.rdt_Table::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.rdt_Table::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}


.rdt_Pagination button:focus,
.rdt_Pagination select:focus {
  outline: 2px solid #007bff !important;
  outline-offset: 2px !important;
}


.rdt_ProgressWrapper .spinner-border {
  width: 2rem !important;
  height: 2rem !important;
}


.rdt_TableCol[role="columnheader"]:hover {
  background-color: #e9ecef !important;
  cursor: pointer !important;
}


.logistics-table .rdt_TableCol {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%) !important;
}

.logistics-table .rdt_Pagination {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%) !important;
}


.rdt_TableRow {
  transition: background-color 0.2s ease !important;
}


.customers-table .rdt_TableCol {
  border-left: 3px solid #007bff !important;
}

.shippers-table .rdt_TableCol {
  border-left: 3px solid #28a745 !important;
}
