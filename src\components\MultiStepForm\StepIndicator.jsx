import React from 'react';
import { FaCheck, FaCircle } from 'react-icons/fa';

const StepIndicator = ({ 
  steps, 
  currentStep, 
  completedSteps = [], 
  variant = 'customer' // 'customer' or 'shipper'
}) => {
  const getStepStatus = (stepIndex) => {
    if (completedSteps.includes(stepIndex)) return 'completed';
    if (stepIndex === currentStep) return 'active';
    return 'pending';
  };

  const getStepColor = (status) => {
    if (variant === 'customer') {
      switch (status) {
        case 'completed': return 'bg-primary text-white';
        case 'active': return 'bg-primary text-white';
        case 'pending': return 'bg-light text-muted';
        default: return 'bg-light text-muted';
      }
    } else {
      switch (status) {
        case 'completed': return 'bg-success text-white';
        case 'active': return 'bg-success text-white';
        case 'pending': return 'bg-light text-muted';
        default: return 'bg-light text-muted';
      }
    }
  };

  const getConnectorColor = (stepIndex) => {
    if (completedSteps.includes(stepIndex) || stepIndex < currentStep) {
      return variant === 'customer' ? 'border-primary' : 'border-success';
    }
    return 'border-light';
  };

  return (
    <div className="step-indicator">
      <div className="d-flex align-items-center justify-content-between position-relative">
        {/* Progress Line */}
        <div 
          className="position-absolute top-50 start-0 translate-middle-y w-100"
          style={{ height: '2px', zIndex: 1 }}
        >
          <div className="h-100 bg-light"></div>
          <div 
            className={`h-100 position-absolute top-0 start-0 ${variant === 'customer' ? 'bg-primary' : 'bg-success'}`}
            style={{ 
              width: `${(currentStep / (steps.length - 1)) * 100}%`,
              transition: 'width 0.3s ease'
            }}
          ></div>
        </div>

        {/* Steps */}
        {steps.map((step, index) => {
          const status = getStepStatus(index);
          const isLast = index === steps.length - 1;
          
          return (
            <div key={index} className="d-flex flex-column align-items-center position-relative" style={{ zIndex: 2 }}>
              {/* Step Circle */}
              <div 
                className={`rounded-circle d-flex align-items-center justify-content-center ${getStepColor(status)}`}
                style={{ 
                  width: '48px', 
                  height: '48px',
                  border: '3px solid white',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
                  transition: 'all 0.3s ease'
                }}
              >
                {status === 'completed' ? (
                  <FaCheck size={18} />
                ) : (
                  <span className="fw-bold">{index + 1}</span>
                )}
              </div>

              {/* Step Info */}
              <div className="text-center mt-3" style={{ maxWidth: '120px' }}>
                <div className={`fw-semibold small ${status === 'active' ? (variant === 'customer' ? 'text-primary' : 'text-success') : 'text-muted'}`}>
                  {step.title}
                </div>
                <div className="text-muted" style={{ fontSize: '11px' }}>
                  {step.description}
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default StepIndicator;
