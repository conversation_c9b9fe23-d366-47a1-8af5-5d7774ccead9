import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import toast from 'react-hot-toast';
import MultiStepFormWrapper from '../../../components/MultiStepForm/MultiStepFormWrapper';
import CompanyDetailsStep from './CompanyDetailsStep';
import ContactFleetStep from './ContactFleetStep';
import AddressOperationsStep from './AddressOperationsStep';
import ReviewStep from './ReviewStep';
import ConfirmationStep from './ConfirmationStep';
import ROUTES from '@constants/routes';

const ShipperCreateFlow = () => {
  const navigate = useNavigate();
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState([]);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const steps = [
    {
      title: 'Company Details',
      description: 'Basic company information',
      component: CompanyDetailsStep
    },
    {
      title: 'Contact & Fleet',
      description: 'Contact and fleet details',
      component: ContactFleetStep
    },
    {
      title: 'Address & Operations',
      description: 'Location and service areas',
      component: AddressOperationsStep
    },
    {
      title: 'Review & Confirm',
      description: 'Verify all information',
      component: ReviewStep
    },
    {
      title: 'Confirmation',
      description: 'Success confirmation',
      component: ConfirmationStep
    }
  ];

  const validationSchemas = [
    // Step 1: Company Details
    Yup.object({
      companyName: Yup.string().required('Company name is required'),
      serviceType: Yup.string().required('Service type is required'),
      gstNumber: Yup.string().matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Invalid GST number format'),
      panNumber: Yup.string().matches(/^[A-Z]{5}[0-9]{4}[A-Z]{1}$/, 'Invalid PAN number format'),
      licenseNumber: Yup.string(),
      insuranceNumber: Yup.string(),
      website: Yup.string().url('Invalid website URL')
    }),
    // Step 2: Contact & Fleet
    Yup.object({
      contactPerson: Yup.string().required('Contact person is required'),
      email: Yup.string().email('Invalid email address').required('Email is required'),
      phone: Yup.string().required('Phone number is required'),
      vehicleCount: Yup.number().required('Vehicle count is required').min(1, 'Must have at least 1 vehicle'),
      ratePerKm: Yup.number().required('Rate per KM is required').min(0, 'Rate must be positive'),
      vehicleTypes: Yup.array().min(1, 'Select at least one vehicle type')
    }),
    // Step 3: Address & Operations
    Yup.object({
      address: Yup.string().required('Address is required'),
      city: Yup.string().required('City is required'),
      state: Yup.string().required('State is required'),
      pincode: Yup.string().required('Pincode is required').matches(/^[0-9]{6}$/, 'Invalid pincode'),
      operatingStates: Yup.array().min(1, 'Select at least one operating state')
    })
  ];

  const formik = useFormik({
    initialValues: {
      // Company Details
      companyName: '',
      serviceType: 'standard',
      gstNumber: '',
      panNumber: '',
      licenseNumber: '',
      insuranceNumber: '',
      website: '',
      
      // Contact & Fleet
      contactPerson: '',
      designation: '',
      email: '',
      phone: '',
      alternatePhone: '',
      vehicleCount: '',
      ratePerKm: '',
      vehicleTypes: [],
      
      // Address & Operations
      address: '',
      city: '',
      state: '',
      pincode: '',
      country: 'India',
      operatingStates: [],
      notes: ''
    },
    validationSchema: validationSchemas[currentStep] || Yup.object(),
    onSubmit: async (values) => {
      if (currentStep < 3) {
        handleNext();
      } else {
        await handleFinalSubmit(values);
      }
    }
  });

  const handleVehicleTypeChange = (type) => {
    const currentTypes = formik.values.vehicleTypes;
    if (currentTypes.includes(type)) {
      formik.setFieldValue('vehicleTypes', currentTypes.filter(t => t !== type));
    } else {
      formik.setFieldValue('vehicleTypes', [...currentTypes, type]);
    }
  };

  const handleOperatingStateChange = (state) => {
    const currentStates = formik.values.operatingStates;
    if (currentStates.includes(state)) {
      formik.setFieldValue('operatingStates', currentStates.filter(s => s !== state));
    } else {
      formik.setFieldValue('operatingStates', [...currentStates, state]);
    }
  };

  const handleNext = async () => {
    const isValid = await formik.validateForm();
    const hasErrors = Object.keys(isValid).length > 0;
    
    if (!hasErrors) {
      setCompletedSteps(prev => [...prev, currentStep]);
      setCurrentStep(prev => prev + 1);
      
      // Update validation schema for next step
      if (currentStep + 1 < validationSchemas.length) {
        formik.setValidationSchema(validationSchemas[currentStep + 1]);
      }
    } else {
      formik.setTouched(
        Object.keys(formik.values).reduce((acc, key) => {
          acc[key] = true;
          return acc;
        }, {})
      );
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => prev - 1);
    if (currentStep - 1 >= 0 && currentStep - 1 < validationSchemas.length) {
      formik.setValidationSchema(validationSchemas[currentStep - 1]);
    }
  };

  const handleFinalSubmit = async (values) => {
    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      console.log('Shipper created:', values);
      toast.success('Shipping partner created successfully!');
      
      setCurrentStep(4); // Move to confirmation step
    } catch (error) {
      toast.error('Failed to create shipping partner. Please try again.');
      console.error('Error creating shipper:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const canProceed = () => {
    if (currentStep >= validationSchemas.length) return true;
    
    const schema = validationSchemas[currentStep];
    try {
      schema.validateSync(formik.values, { abortEarly: false });
      return true;
    } catch {
      return false;
    }
  };

  const CurrentStepComponent = steps[currentStep]?.component;

  return (
    <MultiStepFormWrapper
      steps={steps}
      currentStep={currentStep}
      onNext={handleNext}
      onPrevious={handlePrevious}
      onSubmit={() => handleFinalSubmit(formik.values)}
      isSubmitting={isSubmitting}
      variant="shipper"
      backUrl={ROUTES.SHIPPERS}
      title="Add New Shipping Partner"
      subtitle="Register a trusted logistics partner to expand your delivery network"
      completedSteps={completedSteps}
      canProceed={canProceed()}
      showReview={true}
    >
      {CurrentStepComponent && (
        <CurrentStepComponent 
          formik={formik}
          onNext={handleNext}
          isLastStep={currentStep === steps.length - 1}
          onVehicleTypeChange={handleVehicleTypeChange}
          onOperatingStateChange={handleOperatingStateChange}
        />
      )}
    </MultiStepFormWrapper>
  );
};

export default ShipperCreateFlow;
