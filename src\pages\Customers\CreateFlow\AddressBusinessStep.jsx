import React from 'react';
import { 
  FaMapMarkerAlt, 
  FaBuilding, 
  FaGlobe, 
  FaRupeeSign, 
  FaCalendarAlt,
  FaFileAlt,
  FaInfoCircle,
  FaHandshake
} from 'react-icons/fa';

const AddressBusinessStep = ({ formik }) => {
  return (
    <div className="address-business-step">
      <div className="row">
        <div className="col-lg-8">
          <div className="mb-4">
            <h4 className="text-primary fw-bold mb-2">
              <FaMapMarkerAlt className="me-2" />
              Address & Business Terms
            </h4>
            <p className="text-muted">
              Complete the customer profile with address details and business terms for smooth operations.
            </p>
          </div>

          {/* Address Section */}
          <div className="mb-5">
            <h5 className="text-secondary fw-semibold mb-3">
              <FaBuilding className="me-2" />
              Business Address
            </h5>
            <div className="row g-4">
              <div className="col-12">
                <div className="form-floating">
                  <textarea
                    className={`form-control ${formik.touched.address && formik.errors.address ? 'is-invalid' : ''}`}
                    id="address"
                    name="address"
                    rows="3"
                    value={formik.values.address}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter complete address"
                    style={{ borderRadius: '10px', minHeight: '100px' }}
                  />
                  <label htmlFor="address" className="fw-semibold">
                    <FaMapMarkerAlt className="me-2 text-danger" />
                    Complete Address *
                  </label>
                  {formik.touched.address && formik.errors.address && (
                    <div className="invalid-feedback">{formik.errors.address}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="text"
                    className={`form-control ${formik.touched.city && formik.errors.city ? 'is-invalid' : ''}`}
                    id="city"
                    name="city"
                    value={formik.values.city}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="Enter city"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="city" className="fw-semibold">
                    <FaBuilding className="me-2 text-primary" />
                    City *
                  </label>
                  {formik.touched.city && formik.errors.city && (
                    <div className="invalid-feedback">{formik.errors.city}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <select
                    className={`form-select ${formik.touched.state && formik.errors.state ? 'is-invalid' : ''}`}
                    id="state"
                    name="state"
                    value={formik.values.state}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    style={{ borderRadius: '10px' }}
                  >
                    <option value="">Select State</option>
                    <option value="Maharashtra">Maharashtra</option>
                    <option value="Delhi">Delhi</option>
                    <option value="Karnataka">Karnataka</option>
                    <option value="Tamil Nadu">Tamil Nadu</option>
                    <option value="Gujarat">Gujarat</option>
                    <option value="Rajasthan">Rajasthan</option>
                    <option value="West Bengal">West Bengal</option>
                    <option value="Uttar Pradesh">Uttar Pradesh</option>
                  </select>
                  <label htmlFor="state" className="fw-semibold">
                    <FaMapMarkerAlt className="me-2 text-success" />
                    State *
                  </label>
                  {formik.touched.state && formik.errors.state && (
                    <div className="invalid-feedback">{formik.errors.state}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="text"
                    className={`form-control ${formik.touched.pincode && formik.errors.pincode ? 'is-invalid' : ''}`}
                    id="pincode"
                    name="pincode"
                    value={formik.values.pincode}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="400001"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="pincode" className="fw-semibold">
                    <FaMapMarkerAlt className="me-2 text-warning" />
                    Pincode *
                  </label>
                  {formik.touched.pincode && formik.errors.pincode && (
                    <div className="invalid-feedback">{formik.errors.pincode}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="text"
                    className="form-control"
                    id="country"
                    name="country"
                    value={formik.values.country}
                    onChange={formik.handleChange}
                    readOnly
                    style={{ borderRadius: '10px', backgroundColor: '#f8f9fa' }}
                  />
                  <label htmlFor="country" className="fw-semibold">
                    <FaGlobe className="me-2 text-info" />
                    Country
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Business Terms Section */}
          <div className="mb-4">
            <h5 className="text-secondary fw-semibold mb-3">
              <FaHandshake className="me-2" />
              Business Terms
            </h5>
            <div className="row g-4">
              <div className="col-md-6">
                <div className="form-floating">
                  <input
                    type="number"
                    className={`form-control ${formik.touched.creditLimit && formik.errors.creditLimit ? 'is-invalid' : ''}`}
                    id="creditLimit"
                    name="creditLimit"
                    value={formik.values.creditLimit}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    placeholder="500000"
                    style={{ borderRadius: '10px' }}
                  />
                  <label htmlFor="creditLimit" className="fw-semibold">
                    <FaRupeeSign className="me-2 text-success" />
                    Credit Limit (₹) *
                  </label>
                  {formik.touched.creditLimit && formik.errors.creditLimit && (
                    <div className="invalid-feedback">{formik.errors.creditLimit}</div>
                  )}
                </div>
              </div>

              <div className="col-md-6">
                <div className="form-floating">
                  <select
                    className={`form-select ${formik.touched.paymentTerms && formik.errors.paymentTerms ? 'is-invalid' : ''}`}
                    id="paymentTerms"
                    name="paymentTerms"
                    value={formik.values.paymentTerms}
                    onChange={formik.handleChange}
                    onBlur={formik.handleBlur}
                    style={{ borderRadius: '10px' }}
                  >
                    <option value="15">15 Days</option>
                    <option value="30">30 Days</option>
                    <option value="45">45 Days</option>
                    <option value="60">60 Days</option>
                    <option value="90">90 Days</option>
                  </select>
                  <label htmlFor="paymentTerms" className="fw-semibold">
                    <FaCalendarAlt className="me-2 text-primary" />
                    Payment Terms *
                  </label>
                  {formik.touched.paymentTerms && formik.errors.paymentTerms && (
                    <div className="invalid-feedback">{formik.errors.paymentTerms}</div>
                  )}
                </div>
              </div>

              <div className="col-12">
                <div className="form-floating">
                  <textarea
                    className="form-control"
                    id="notes"
                    name="notes"
                    rows="4"
                    value={formik.values.notes}
                    onChange={formik.handleChange}
                    placeholder="Any additional notes or special instructions..."
                    style={{ borderRadius: '10px', minHeight: '120px' }}
                  />
                  <label htmlFor="notes" className="fw-semibold">
                    <FaFileAlt className="me-2 text-info" />
                    Special Instructions & Notes (Optional)
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-4">
          {/* Address Help */}
          <div className="card border-0 bg-light mb-3">
            <div className="card-body p-4">
              <h6 className="fw-bold text-primary mb-3">
                <FaInfoCircle className="me-2" />
                Address Guidelines
              </h6>
              <ul className="list-unstyled mb-0 small">
                <li className="mb-2">
                  <strong>Complete Address:</strong> Include building name, street, and landmarks for accurate delivery.
                </li>
                <li className="mb-2">
                  <strong>Pincode:</strong> Essential for logistics planning and delivery routing.
                </li>
                <li className="mb-2">
                  <strong>State:</strong> Required for GST compliance and interstate logistics.
                </li>
              </ul>
            </div>
          </div>

          {/* Business Terms Help */}
          <div className="card border-0 bg-warning bg-opacity-10">
            <div className="card-body p-4">
              <h6 className="fw-bold text-warning mb-3">
                <FaHandshake className="me-2" />
                Business Terms Guide
              </h6>
              <div className="small">
                <div className="mb-2">
                  <strong>Credit Limit:</strong> Maximum outstanding amount allowed for this customer.
                </div>
                <div className="mb-2">
                  <strong>Payment Terms:</strong> Number of days customer has to pay invoices.
                </div>
                <div className="mb-2">
                  <strong>Notes:</strong> Special handling instructions, delivery preferences, or account notes.
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddressBusinessStep;
