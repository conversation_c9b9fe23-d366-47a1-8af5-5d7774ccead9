import ROUTES from "@constants/routes";
import AuthLayout from "@layouts/AuthLayout";
import MainLayout from "@layouts/MainLayout";
import Login from "@pages/Authentication/Login";
import ForgotPassword from "@pages/Authentication/ForgotPassword";
import Home from "@pages/Home";
import Customers from "@pages/Customers";
import CreateCustomer from "@pages/Customers/Create";
import CustomerCreateFlow from "@pages/Customers/CreateFlow";
import Shippers from "@pages/Shippers";
import CreateShipper from "@pages/Shippers/Create";
import ShipperCreateFlow from "@pages/Shippers/CreateFlow";
import React from "react";
import { BrowserRouter, Route, Routes } from "react-router-dom";

const AppRouter = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route element={<AuthLayout />}>
          <Route path={ROUTES.LOGIN} element={<Login />} />
          <Route path={ROUTES.FORGOT_PASSWORD} element={<ForgotPassword />} />
        </Route>
        <Route element={<MainLayout />}>
          <Route index element={<Home />} />

          {/* Customers Routes */}
          <Route path={ROUTES.CUSTOMERS} element={<Customers />} />
          <Route path={ROUTES.CUSTOMERS_CREATE} element={<CreateCustomer />} />
          <Route path={ROUTES.CUSTOMERS_CREATE_FLOW} element={<CustomerCreateFlow />} />

          {/* Shippers Routes */}
          <Route path={ROUTES.SHIPPERS} element={<Shippers />} />
          <Route path={ROUTES.SHIPPERS_CREATE} element={<CreateShipper />} />
          <Route path={ROUTES.SHIPPERS_CREATE_FLOW} element={<ShipperCreateFlow />} />
        </Route>
      </Routes>
    </BrowserRouter>
  );
};

export default AppRouter;
