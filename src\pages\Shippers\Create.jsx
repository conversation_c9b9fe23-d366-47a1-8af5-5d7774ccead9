import React, { useState } from 'react';
import { <PERSON>, useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import toast from 'react-hot-toast';
import {
  FaArrowLeft,
  FaSave,
  FaUser,
  FaTruck,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaRupeeSign,
  FaFileAlt,
  FaGlobe,
  FaShippingFast,
  FaCertificate,
  FaCheckCircle,
  FaIdCard,
  FaCreditCard,
  FaCalendarAlt,
  FaUserTie,
  FaIndustry,
  FaShieldAlt,
  FaChartLine,
  FaRoute,
  FaCogs
} from 'react-icons/fa';
import ROUTES from '@constants/routes';

const CreateShipper = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation schema
  const validationSchema = Yup.object({
    companyName: Yup.string()
      .required('Company name is required')
      .min(2, 'Company name must be at least 2 characters'),
    contactPerson: Yup.string()
      .required('Contact person name is required')
      .min(2, 'Contact person name must be at least 2 characters'),
    email: Yup.string()
      .email('Please enter a valid email address')
      .required('Email is required'),
    phone: Yup.string()
      .required('Phone number is required')
      .matches(/^[+]?[\d\s-()]+$/, 'Please enter a valid phone number'),
    alternatePhone: Yup.string()
      .matches(/^[+]?[\d\s-()]+$/, 'Please enter a valid phone number'),
    address: Yup.string()
      .required('Address is required')
      .min(10, 'Address must be at least 10 characters'),
    city: Yup.string()
      .required('City is required'),
    state: Yup.string()
      .required('State is required'),
    pincode: Yup.string()
      .required('Pincode is required')
      .matches(/^\d{6}$/, 'Pincode must be 6 digits'),
    gstNumber: Yup.string()
      .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Please enter a valid GST number'),
    serviceType: Yup.string()
      .required('Service type is required'),
    vehicleCount: Yup.number()
      .min(1, 'Vehicle count must be at least 1')
      .required('Vehicle count is required'),
    ratePerKm: Yup.number()
      .min(0, 'Rate per km cannot be negative')
      .required('Rate per km is required'),
  });

  // Form handling
  const formik = useFormik({
    initialValues: {
      companyName: '',
      contactPerson: '',
      designation: '',
      email: '',
      phone: '',
      alternatePhone: '',
      address: '',
      city: '',
      state: '',
      pincode: '',
      country: 'India',
      gstNumber: '',
      panNumber: '',
      serviceType: 'standard',
      vehicleTypes: [],
      vehicleCount: '',
      ratePerKm: '',
      operatingStates: [],
      licenseNumber: '',
      insuranceNumber: '',
      website: '',
      notes: ''
    },
    validationSchema,
    onSubmit: async (values) => {
      setIsSubmitting(true);
      try {
        // Simulate API call
        console.log('Creating shipper:', values);
        await new Promise(resolve => setTimeout(resolve, 1500));

        toast.success('Shipper created successfully!');
        navigate(ROUTES.SHIPPERS);
      } catch (error) {
        toast.error('Failed to create shipper. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  const handleVehicleTypeChange = (vehicleType) => {
    const currentTypes = formik.values.vehicleTypes;
    if (currentTypes.includes(vehicleType)) {
      formik.setFieldValue('vehicleTypes', currentTypes.filter(type => type !== vehicleType));
    } else {
      formik.setFieldValue('vehicleTypes', [...currentTypes, vehicleType]);
    }
  };

  const handleOperatingStateChange = (state) => {
    const currentStates = formik.values.operatingStates;
    if (currentStates.includes(state)) {
      formik.setFieldValue('operatingStates', currentStates.filter(s => s !== state));
    } else {
      formik.setFieldValue('operatingStates', [...currentStates, state]);
    }
  };

  return (
    <div className="create-shipper-page">
      {/* Modern Page Header with Gradient Background */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-lg" style={{
            background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
            borderRadius: '20px'
          }}>
            <div className="card-body p-4">
              <div className="d-flex align-items-center text-white">
                <Link to={ROUTES.SHIPPERS} className="btn btn-light btn-sm me-3 rounded-pill">
                  <FaArrowLeft className="me-2" />
                  Back to Shippers
                </Link>
                <div className="flex-grow-1">
                  <h1 className="h2 mb-2 text-white fw-bold d-flex align-items-center">
                    <div className="bg-white bg-opacity-20 rounded-circle p-3 me-3">
                      <FaTruck className="text-white" size={24} />
                    </div>
                    Add New Shipping Partner
                  </h1>
                  <p className="text-white-50 mb-0 fs-5">
                    Register a trusted logistics partner to expand your delivery network
                  </p>
                </div>
                <div className="text-end">
                  <div className="bg-white bg-opacity-20 rounded-circle p-3">
                    <FaShippingFast className="text-white" size={32} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body py-3">
              <div className="d-flex justify-content-between align-items-center">
                <div className="d-flex align-items-center">
                  <div className="bg-success rounded-circle p-2 me-3">
                    <FaTruck className="text-white" size={16} />
                  </div>
                  <span className="fw-semibold text-success">Company Details</span>
                </div>
                <div className="border-top flex-grow-1 mx-3"></div>
                <div className="d-flex align-items-center">
                  <div className="bg-light rounded-circle p-2 me-3">
                    <FaUser className="text-muted" size={16} />
                  </div>
                  <span className="text-muted">Contact & Fleet Info</span>
                </div>
                <div className="border-top flex-grow-1 mx-3"></div>
                <div className="d-flex align-items-center">
                  <div className="bg-light rounded-circle p-2 me-3">
                    <FaMapMarkerAlt className="text-muted" size={16} />
                  </div>
                  <span className="text-muted">Address & Operations</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <div className="row">
          {/* Main Form */}
          <div className="col-lg-8">
            {/* Company Information */}
            <div className="card border-0 shadow-lg mb-4" style={{ borderRadius: '15px' }}>
              <div className="card-header border-0" style={{
                background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
                borderRadius: '15px 15px 0 0'
              }}>
                <div className="d-flex align-items-center text-white py-2">
                  <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                    <FaTruck className="text-white" size={20} />
                  </div>
                  <h5 className="mb-0 fw-bold text-white">Shipping Company Information</h5>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-4">
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className={`form-control ${formik.touched.companyName && formik.errors.companyName ? 'is-invalid' : ''}`}
                        id="companyName"
                        name="companyName"
                        value={formik.values.companyName}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter company name"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="companyName" className="fw-semibold">
                        <FaTruck className="me-2 text-success" />
                        Company Name *
                      </label>
                      {formik.touched.companyName && formik.errors.companyName && (
                        <div className="invalid-feedback">{formik.errors.companyName}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <select
                        className={`form-select ${formik.touched.serviceType && formik.errors.serviceType ? 'is-invalid' : ''}`}
                        id="serviceType"
                        name="serviceType"
                        value={formik.values.serviceType}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        style={{ borderRadius: '10px' }}
                      >
                        <option value="standard">🚛 Standard Delivery</option>
                        <option value="express">⚡ Express Delivery</option>
                        <option value="heavy">🏗️ Heavy Cargo</option>
                        <option value="refrigerated">❄️ Refrigerated Transport</option>
                        <option value="hazardous">⚠️ Hazardous Materials</option>
                      </select>
                      <label htmlFor="serviceType" className="fw-semibold">
                        <FaRoute className="me-2 text-primary" />
                        Service Type *
                      </label>
                      {formik.touched.serviceType && formik.errors.serviceType && (
                        <div className="invalid-feedback">{formik.errors.serviceType}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className={`form-control ${formik.touched.gstNumber && formik.errors.gstNumber ? 'is-invalid' : ''}`}
                        id="gstNumber"
                        name="gstNumber"
                        value={formik.values.gstNumber}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="22AAAAA0000A1Z5"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="gstNumber" className="fw-semibold">
                        <FaShieldAlt className="me-2 text-warning" />
                        GST Number
                      </label>
                      {formik.touched.gstNumber && formik.errors.gstNumber && (
                        <div className="invalid-feedback">{formik.errors.gstNumber}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className="form-control"
                        id="panNumber"
                        name="panNumber"
                        value={formik.values.panNumber}
                        onChange={formik.handleChange}
                        placeholder="**********"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="panNumber" className="fw-semibold">
                        <FaIdCard className="me-2 text-info" />
                        PAN Number
                      </label>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className="form-control"
                        id="licenseNumber"
                        name="licenseNumber"
                        value={formik.values.licenseNumber}
                        onChange={formik.handleChange}
                        placeholder="TL123456789"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="licenseNumber" className="fw-semibold">
                        <FaCertificate className="me-2 text-warning" />
                        Transport License Number
                      </label>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className="form-control"
                        id="insuranceNumber"
                        name="insuranceNumber"
                        value={formik.values.insuranceNumber}
                        onChange={formik.handleChange}
                        placeholder="INS123456789"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="insuranceNumber" className="fw-semibold">
                        <FaShieldAlt className="me-2 text-danger" />
                        Insurance Policy Number
                      </label>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="form-floating">
                      <input
                        type="url"
                        className="form-control"
                        id="website"
                        name="website"
                        value={formik.values.website}
                        onChange={formik.handleChange}
                        placeholder="https://www.company.com"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="website" className="fw-semibold">
                        <FaGlobe className="me-2 text-primary" />
                        Company Website
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="card border-0 shadow-lg mb-4" style={{ borderRadius: '15px' }}>
              <div className="card-header border-0" style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '15px 15px 0 0'
              }}>
                <div className="d-flex align-items-center text-white py-2">
                  <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                    <FaUser className="text-white" size={20} />
                  </div>
                  <h5 className="mb-0 fw-bold text-white">Contact Information</h5>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-4">
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className={`form-control ${formik.touched.contactPerson && formik.errors.contactPerson ? 'is-invalid' : ''}`}
                        id="contactPerson"
                        name="contactPerson"
                        value={formik.values.contactPerson}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter contact person name"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="contactPerson" className="fw-semibold">
                        <FaUser className="me-2 text-primary" />
                        Contact Person *
                      </label>
                      {formik.touched.contactPerson && formik.errors.contactPerson && (
                        <div className="invalid-feedback">{formik.errors.contactPerson}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className="form-control"
                        id="designation"
                        name="designation"
                        value={formik.values.designation}
                        onChange={formik.handleChange}
                        placeholder="Manager, Director, etc."
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="designation" className="fw-semibold">
                        <FaUserTie className="me-2 text-success" />
                        Designation
                      </label>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="email"
                        className={`form-control ${formik.touched.email && formik.errors.email ? 'is-invalid' : ''}`}
                        id="email"
                        name="email"
                        value={formik.values.email}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="<EMAIL>"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="email" className="fw-semibold">
                        <FaEnvelope className="me-2 text-info" />
                        Email Address *
                      </label>
                      {formik.touched.email && formik.errors.email && (
                        <div className="invalid-feedback">{formik.errors.email}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="tel"
                        className={`form-control ${formik.touched.phone && formik.errors.phone ? 'is-invalid' : ''}`}
                        id="phone"
                        name="phone"
                        value={formik.values.phone}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="+91 98765 43210"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="phone" className="fw-semibold">
                        <FaPhone className="me-2 text-warning" />
                        Phone Number *
                      </label>
                      {formik.touched.phone && formik.errors.phone && (
                        <div className="invalid-feedback">{formik.errors.phone}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="tel"
                        className={`form-control ${formik.touched.alternatePhone && formik.errors.alternatePhone ? 'is-invalid' : ''}`}
                        id="alternatePhone"
                        name="alternatePhone"
                        value={formik.values.alternatePhone}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="+91 87654 32109"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="alternatePhone" className="fw-semibold">
                        <FaPhone className="me-2 text-secondary" />
                        Alternate Phone
                      </label>
                      {formik.touched.alternatePhone && formik.errors.alternatePhone && (
                        <div className="invalid-feedback">{formik.errors.alternatePhone}</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Fleet Information */}
            <div className="card border-0 shadow-lg mb-4" style={{ borderRadius: '15px' }}>
              <div className="card-header border-0" style={{
                background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
                borderRadius: '15px 15px 0 0'
              }}>
                <div className="d-flex align-items-center text-white py-2">
                  <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                    <FaShippingFast className="text-white" size={20} />
                  </div>
                  <h5 className="mb-0 fw-bold text-white">Fleet & Pricing Information</h5>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-4">
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="number"
                        className={`form-control ${formik.touched.vehicleCount && formik.errors.vehicleCount ? 'is-invalid' : ''}`}
                        id="vehicleCount"
                        name="vehicleCount"
                        value={formik.values.vehicleCount}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="25"
                        min="1"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="vehicleCount" className="fw-semibold">
                        <FaTruck className="me-2 text-primary" />
                        Total Vehicle Count *
                      </label>
                      {formik.touched.vehicleCount && formik.errors.vehicleCount && (
                        <div className="invalid-feedback">{formik.errors.vehicleCount}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="number"
                        className={`form-control ${formik.touched.ratePerKm && formik.errors.ratePerKm ? 'is-invalid' : ''}`}
                        id="ratePerKm"
                        name="ratePerKm"
                        value={formik.values.ratePerKm}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="15.50"
                        step="0.01"
                        min="0"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="ratePerKm" className="fw-semibold">
                        <FaRupeeSign className="me-2 text-success" />
                        Rate per KM (₹) *
                      </label>
                      {formik.touched.ratePerKm && formik.errors.ratePerKm && (
                        <div className="invalid-feedback">{formik.errors.ratePerKm}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold mb-3">
                      <FaCogs className="me-2 text-info" />
                      Available Vehicle Types
                    </label>
                    <div className="row g-3">
                      {[
                        { type: 'Mini Truck', icon: '🚐' },
                        { type: 'Small Truck', icon: '🚚' },
                        { type: 'Medium Truck', icon: '🚛' },
                        { type: 'Large Truck', icon: '🚜' },
                        { type: 'Container', icon: '📦' },
                        { type: 'Trailer', icon: '🚛' },
                        { type: 'Refrigerated', icon: '❄️' },
                        { type: 'Tanker', icon: '🛢️' }
                      ].map(({ type, icon }) => (
                        <div key={type} className="col-md-3">
                          <div className="form-check">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              id={`vehicle-${type}`}
                              checked={formik.values.vehicleTypes.includes(type)}
                              onChange={() => handleVehicleTypeChange(type)}
                              style={{ transform: 'scale(1.2)' }}
                            />
                            <label className="form-check-label fw-semibold" htmlFor={`vehicle-${type}`}>
                              <span className="me-2">{icon}</span>
                              {type}
                            </label>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="col-lg-4">
            {/* Address Information */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaMapMarkerAlt className="me-2 text-primary" />
                  Address Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-12">
                    <label className="form-label fw-semibold">Address *</label>
                    <textarea
                      className={`form-control ${formik.touched.address && formik.errors.address ? 'is-invalid' : ''}`}
                      name="address"
                      rows="3"
                      value={formik.values.address}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter complete address"
                    />
                    {formik.touched.address && formik.errors.address && (
                      <div className="invalid-feedback">{formik.errors.address}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">City *</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.city && formik.errors.city ? 'is-invalid' : ''}`}
                      name="city"
                      value={formik.values.city}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter city"
                    />
                    {formik.touched.city && formik.errors.city && (
                      <div className="invalid-feedback">{formik.errors.city}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">State *</label>
                    <select
                      className={`form-select ${formik.touched.state && formik.errors.state ? 'is-invalid' : ''}`}
                      name="state"
                      value={formik.values.state}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    >
                      <option value="">Select State</option>
                      <option value="Maharashtra">Maharashtra</option>
                      <option value="Delhi">Delhi</option>
                      <option value="Karnataka">Karnataka</option>
                      <option value="Tamil Nadu">Tamil Nadu</option>
                      <option value="Gujarat">Gujarat</option>
                      <option value="Rajasthan">Rajasthan</option>
                      <option value="West Bengal">West Bengal</option>
                      <option value="Uttar Pradesh">Uttar Pradesh</option>
                    </select>
                    {formik.touched.state && formik.errors.state && (
                      <div className="invalid-feedback">{formik.errors.state}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">Pincode *</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.pincode && formik.errors.pincode ? 'is-invalid' : ''}`}
                      name="pincode"
                      value={formik.values.pincode}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="400001"
                    />
                    {formik.touched.pincode && formik.errors.pincode && (
                      <div className="invalid-feedback">{formik.errors.pincode}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">Country</label>
                    <input
                      type="text"
                      className="form-control"
                      name="country"
                      value={formik.values.country}
                      onChange={formik.handleChange}
                      readOnly
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Operating States */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaMapMarkerAlt className="me-2 text-primary" />
                  Operating States
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-2">
                  {['Maharashtra', 'Delhi', 'Karnataka', 'Tamil Nadu', 'Gujarat', 'Rajasthan', 'West Bengal', 'Uttar Pradesh'].map((state) => (
                    <div key={state} className="col-12">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id={`state-${state}`}
                          checked={formik.values.operatingStates.includes(state)}
                          onChange={() => handleOperatingStateChange(state)}
                        />
                        <label className="form-check-label" htmlFor={`state-${state}`}>
                          {state}
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Additional Notes */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaFileAlt className="me-2 text-primary" />
                  Additional Notes
                </h5>
              </div>
              <div className="card-body">
                <textarea
                  className="form-control"
                  name="notes"
                  rows="4"
                  value={formik.values.notes}
                  onChange={formik.handleChange}
                  placeholder="Any additional notes, special services, or requirements..."
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="card border-0 shadow-lg" style={{ borderRadius: '15px' }}>
              <div className="card-body p-4">
                <div className="d-grid gap-3">
                  <button
                    type="submit"
                    className="btn btn-lg text-white fw-bold"
                    disabled={isSubmitting}
                    style={{
                      background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
                      border: 'none',
                      borderRadius: '12px',
                      padding: '15px',
                      boxShadow: '0 8px 25px rgba(17, 153, 142, 0.3)'
                    }}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="spinner-border spinner-border-sm me-2" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                        Creating Shipper...
                      </>
                    ) : (
                      <>
                        <FaCheckCircle className="me-2" />
                        Register Shipping Partner
                      </>
                    )}
                  </button>
                  <Link
                    to={ROUTES.SHIPPERS}
                    className="btn btn-outline-secondary btn-lg fw-semibold"
                    style={{ borderRadius: '12px', padding: '15px' }}
                  >
                    <FaArrowLeft className="me-2" />
                    Cancel & Go Back
                  </Link>
                </div>

                {/* Quick Tips */}
                <div className="mt-4 p-3 bg-light rounded-3">
                  <h6 className="fw-bold text-success mb-2">
                    <FaTruck className="me-2" />
                    Shipping Partner Tips
                  </h6>
                  <ul className="list-unstyled mb-0 small text-muted">
                    <li className="mb-1">• Verify transport license and insurance validity</li>
                    <li className="mb-1">• Set competitive rates based on market standards</li>
                    <li>• Select operating states for better route planning</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CreateShipper;
