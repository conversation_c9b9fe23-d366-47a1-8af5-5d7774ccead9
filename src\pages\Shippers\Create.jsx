import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import toast from 'react-hot-toast';
import {
  FaArrowLeft,
  FaSave,
  FaUser,
  FaTruck,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaRupeeSign,
  FaFileAlt,
  FaGlobe,
  FaShippingFast,
  FaCertificate
} from 'react-icons/fa';
import ROUTES from '@constants/routes';

const CreateShipper = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation schema
  const validationSchema = Yup.object({
    companyName: Yup.string()
      .required('Company name is required')
      .min(2, 'Company name must be at least 2 characters'),
    contactPerson: Yup.string()
      .required('Contact person name is required')
      .min(2, 'Contact person name must be at least 2 characters'),
    email: Yup.string()
      .email('Please enter a valid email address')
      .required('Email is required'),
    phone: Yup.string()
      .required('Phone number is required')
      .matches(/^[+]?[\d\s-()]+$/, 'Please enter a valid phone number'),
    alternatePhone: Yup.string()
      .matches(/^[+]?[\d\s-()]+$/, 'Please enter a valid phone number'),
    address: Yup.string()
      .required('Address is required')
      .min(10, 'Address must be at least 10 characters'),
    city: Yup.string()
      .required('City is required'),
    state: Yup.string()
      .required('State is required'),
    pincode: Yup.string()
      .required('Pincode is required')
      .matches(/^\d{6}$/, 'Pincode must be 6 digits'),
    gstNumber: Yup.string()
      .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Please enter a valid GST number'),
    serviceType: Yup.string()
      .required('Service type is required'),
    vehicleCount: Yup.number()
      .min(1, 'Vehicle count must be at least 1')
      .required('Vehicle count is required'),
    ratePerKm: Yup.number()
      .min(0, 'Rate per km cannot be negative')
      .required('Rate per km is required'),
  });

  // Form handling
  const formik = useFormik({
    initialValues: {
      companyName: '',
      contactPerson: '',
      designation: '',
      email: '',
      phone: '',
      alternatePhone: '',
      address: '',
      city: '',
      state: '',
      pincode: '',
      country: 'India',
      gstNumber: '',
      panNumber: '',
      serviceType: 'standard',
      vehicleTypes: [],
      vehicleCount: '',
      ratePerKm: '',
      operatingStates: [],
      licenseNumber: '',
      insuranceNumber: '',
      website: '',
      notes: ''
    },
    validationSchema,
    onSubmit: async (values) => {
      setIsSubmitting(true);
      try {
        // Simulate API call
        console.log('Creating shipper:', values);
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        toast.success('Shipper created successfully!');
        navigate(ROUTES.SHIPPERS);
      } catch (error) {
        toast.error('Failed to create shipper. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  const handleVehicleTypeChange = (vehicleType) => {
    const currentTypes = formik.values.vehicleTypes;
    if (currentTypes.includes(vehicleType)) {
      formik.setFieldValue('vehicleTypes', currentTypes.filter(type => type !== vehicleType));
    } else {
      formik.setFieldValue('vehicleTypes', [...currentTypes, vehicleType]);
    }
  };

  const handleOperatingStateChange = (state) => {
    const currentStates = formik.values.operatingStates;
    if (currentStates.includes(state)) {
      formik.setFieldValue('operatingStates', currentStates.filter(s => s !== state));
    } else {
      formik.setFieldValue('operatingStates', [...currentStates, state]);
    }
  };

  return (
    <div className="create-shipper-page">
      {/* Page Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex align-items-center">
            <Link to={ROUTES.SHIPPERS} className="btn btn-outline-secondary me-3">
              <FaArrowLeft className="me-2" />
              Back
            </Link>
            <div>
              <h1 className="h3 mb-1 text-dark fw-bold">
                <FaTruck className="me-2 text-primary" />
                Add New Shipper
              </h1>
              <p className="text-muted mb-0">Register a new shipping partner for your logistics network</p>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <div className="row">
          {/* Main Form */}
          <div className="col-lg-8">
            {/* Company Information */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaTruck className="me-2 text-primary" />
                  Company Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Company Name *</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.companyName && formik.errors.companyName ? 'is-invalid' : ''}`}
                      name="companyName"
                      value={formik.values.companyName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter company name"
                    />
                    {formik.touched.companyName && formik.errors.companyName && (
                      <div className="invalid-feedback">{formik.errors.companyName}</div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Service Type *</label>
                    <select
                      className={`form-select ${formik.touched.serviceType && formik.errors.serviceType ? 'is-invalid' : ''}`}
                      name="serviceType"
                      value={formik.values.serviceType}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    >
                      <option value="standard">Standard Delivery</option>
                      <option value="express">Express Delivery</option>
                      <option value="heavy">Heavy Cargo</option>
                      <option value="refrigerated">Refrigerated Transport</option>
                      <option value="hazardous">Hazardous Materials</option>
                    </select>
                    {formik.touched.serviceType && formik.errors.serviceType && (
                      <div className="invalid-feedback">{formik.errors.serviceType}</div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">GST Number</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.gstNumber && formik.errors.gstNumber ? 'is-invalid' : ''}`}
                      name="gstNumber"
                      value={formik.values.gstNumber}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="22AAAAA0000A1Z5"
                    />
                    {formik.touched.gstNumber && formik.errors.gstNumber && (
                      <div className="invalid-feedback">{formik.errors.gstNumber}</div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">PAN Number</label>
                    <input
                      type="text"
                      className="form-control"
                      name="panNumber"
                      value={formik.values.panNumber}
                      onChange={formik.handleChange}
                      placeholder="**********"
                    />
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Transport License Number</label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaCertificate />
                      </span>
                      <input
                        type="text"
                        className="form-control"
                        name="licenseNumber"
                        value={formik.values.licenseNumber}
                        onChange={formik.handleChange}
                        placeholder="TL123456789"
                      />
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Insurance Policy Number</label>
                    <input
                      type="text"
                      className="form-control"
                      name="insuranceNumber"
                      value={formik.values.insuranceNumber}
                      onChange={formik.handleChange}
                      placeholder="INS123456789"
                    />
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">Website</label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaGlobe />
                      </span>
                      <input
                        type="url"
                        className="form-control"
                        name="website"
                        value={formik.values.website}
                        onChange={formik.handleChange}
                        placeholder="https://www.company.com"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaUser className="me-2 text-primary" />
                  Contact Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Contact Person *</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.contactPerson && formik.errors.contactPerson ? 'is-invalid' : ''}`}
                      name="contactPerson"
                      value={formik.values.contactPerson}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter contact person name"
                    />
                    {formik.touched.contactPerson && formik.errors.contactPerson && (
                      <div className="invalid-feedback">{formik.errors.contactPerson}</div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Designation</label>
                    <input
                      type="text"
                      className="form-control"
                      name="designation"
                      value={formik.values.designation}
                      onChange={formik.handleChange}
                      placeholder="Manager, Director, etc."
                    />
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Email Address *</label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaEnvelope />
                      </span>
                      <input
                        type="email"
                        className={`form-control ${formik.touched.email && formik.errors.email ? 'is-invalid' : ''}`}
                        name="email"
                        value={formik.values.email}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="<EMAIL>"
                      />
                      {formik.touched.email && formik.errors.email && (
                        <div className="invalid-feedback">{formik.errors.email}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Phone Number *</label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaPhone />
                      </span>
                      <input
                        type="tel"
                        className={`form-control ${formik.touched.phone && formik.errors.phone ? 'is-invalid' : ''}`}
                        name="phone"
                        value={formik.values.phone}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="+91 98765 43210"
                      />
                      {formik.touched.phone && formik.errors.phone && (
                        <div className="invalid-feedback">{formik.errors.phone}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Alternate Phone</label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaPhone />
                      </span>
                      <input
                        type="tel"
                        className={`form-control ${formik.touched.alternatePhone && formik.errors.alternatePhone ? 'is-invalid' : ''}`}
                        name="alternatePhone"
                        value={formik.values.alternatePhone}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="+91 87654 32109"
                      />
                      {formik.touched.alternatePhone && formik.errors.alternatePhone && (
                        <div className="invalid-feedback">{formik.errors.alternatePhone}</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Fleet Information */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaShippingFast className="me-2 text-primary" />
                  Fleet Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Total Vehicle Count *</label>
                    <input
                      type="number"
                      className={`form-control ${formik.touched.vehicleCount && formik.errors.vehicleCount ? 'is-invalid' : ''}`}
                      name="vehicleCount"
                      value={formik.values.vehicleCount}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="25"
                      min="1"
                    />
                    {formik.touched.vehicleCount && formik.errors.vehicleCount && (
                      <div className="invalid-feedback">{formik.errors.vehicleCount}</div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Rate per KM *</label>
                    <div className="input-group">
                      <span className="input-group-text">₹</span>
                      <input
                        type="number"
                        className={`form-control ${formik.touched.ratePerKm && formik.errors.ratePerKm ? 'is-invalid' : ''}`}
                        name="ratePerKm"
                        value={formik.values.ratePerKm}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="15.50"
                        step="0.01"
                        min="0"
                      />
                      {formik.touched.ratePerKm && formik.errors.ratePerKm && (
                        <div className="invalid-feedback">{formik.errors.ratePerKm}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">Vehicle Types</label>
                    <div className="row g-2">
                      {['Mini Truck', 'Small Truck', 'Medium Truck', 'Large Truck', 'Container', 'Trailer', 'Refrigerated', 'Tanker'].map((type) => (
                        <div key={type} className="col-md-3">
                          <div className="form-check">
                            <input
                              className="form-check-input"
                              type="checkbox"
                              id={`vehicle-${type}`}
                              checked={formik.values.vehicleTypes.includes(type)}
                              onChange={() => handleVehicleTypeChange(type)}
                            />
                            <label className="form-check-label" htmlFor={`vehicle-${type}`}>
                              {type}
                            </label>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="col-lg-4">
            {/* Address Information */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaMapMarkerAlt className="me-2 text-primary" />
                  Address Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-12">
                    <label className="form-label fw-semibold">Address *</label>
                    <textarea
                      className={`form-control ${formik.touched.address && formik.errors.address ? 'is-invalid' : ''}`}
                      name="address"
                      rows="3"
                      value={formik.values.address}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter complete address"
                    />
                    {formik.touched.address && formik.errors.address && (
                      <div className="invalid-feedback">{formik.errors.address}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">City *</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.city && formik.errors.city ? 'is-invalid' : ''}`}
                      name="city"
                      value={formik.values.city}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter city"
                    />
                    {formik.touched.city && formik.errors.city && (
                      <div className="invalid-feedback">{formik.errors.city}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">State *</label>
                    <select
                      className={`form-select ${formik.touched.state && formik.errors.state ? 'is-invalid' : ''}`}
                      name="state"
                      value={formik.values.state}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    >
                      <option value="">Select State</option>
                      <option value="Maharashtra">Maharashtra</option>
                      <option value="Delhi">Delhi</option>
                      <option value="Karnataka">Karnataka</option>
                      <option value="Tamil Nadu">Tamil Nadu</option>
                      <option value="Gujarat">Gujarat</option>
                      <option value="Rajasthan">Rajasthan</option>
                      <option value="West Bengal">West Bengal</option>
                      <option value="Uttar Pradesh">Uttar Pradesh</option>
                    </select>
                    {formik.touched.state && formik.errors.state && (
                      <div className="invalid-feedback">{formik.errors.state}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">Pincode *</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.pincode && formik.errors.pincode ? 'is-invalid' : ''}`}
                      name="pincode"
                      value={formik.values.pincode}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="400001"
                    />
                    {formik.touched.pincode && formik.errors.pincode && (
                      <div className="invalid-feedback">{formik.errors.pincode}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">Country</label>
                    <input
                      type="text"
                      className="form-control"
                      name="country"
                      value={formik.values.country}
                      onChange={formik.handleChange}
                      readOnly
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Operating States */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaMapMarkerAlt className="me-2 text-primary" />
                  Operating States
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-2">
                  {['Maharashtra', 'Delhi', 'Karnataka', 'Tamil Nadu', 'Gujarat', 'Rajasthan', 'West Bengal', 'Uttar Pradesh'].map((state) => (
                    <div key={state} className="col-12">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          id={`state-${state}`}
                          checked={formik.values.operatingStates.includes(state)}
                          onChange={() => handleOperatingStateChange(state)}
                        />
                        <label className="form-check-label" htmlFor={`state-${state}`}>
                          {state}
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Additional Notes */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaFileAlt className="me-2 text-primary" />
                  Additional Notes
                </h5>
              </div>
              <div className="card-body">
                <textarea
                  className="form-control"
                  name="notes"
                  rows="4"
                  value={formik.values.notes}
                  onChange={formik.handleChange}
                  placeholder="Any additional notes, special services, or requirements..."
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="card border-0 shadow-sm">
              <div className="card-body">
                <div className="d-grid gap-2">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="spinner-border spinner-border-sm me-2" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <FaSave className="me-2" />
                        Create Shipper
                      </>
                    )}
                  </button>
                  <Link to={ROUTES.SHIPPERS} className="btn btn-outline-secondary">
                    Cancel
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CreateShipper;
