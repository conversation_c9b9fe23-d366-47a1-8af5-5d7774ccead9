import React from 'react';
import { 
  FaTruck, 
  FaRoute, 
  FaShieldAlt, 
  FaIdCard, 
  FaGlobe,
  FaInfoCircle,
  FaCertificate
} from 'react-icons/fa';

const CompanyDetailsStep = ({ formik }) => {
  return (
    <div className="company-details-step">
      <div className="row">
        <div className="col-lg-8">
          <div className="mb-4">
            <h4 className="text-success fw-bold mb-2">
              <FaTruck className="me-2" />
              Shipping Company Information
            </h4>
            <p className="text-muted">
              Enter the basic company details for your new shipping partner. This information will be used for logistics coordination and compliance.
            </p>
          </div>

          <div className="row g-4">
            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="text"
                  className={`form-control ${formik.touched.companyName && formik.errors.companyName ? 'is-invalid' : ''}`}
                  id="companyName"
                  name="companyName"
                  value={formik.values.companyName}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="Enter company name"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="companyName" className="fw-semibold">
                  <FaTruck className="me-2 text-success" />
                  Company Name *
                </label>
                {formik.touched.companyName && formik.errors.companyName && (
                  <div className="invalid-feedback">{formik.errors.companyName}</div>
                )}
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <select
                  className={`form-select ${formik.touched.serviceType && formik.errors.serviceType ? 'is-invalid' : ''}`}
                  id="serviceType"
                  name="serviceType"
                  value={formik.values.serviceType}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  style={{ borderRadius: '10px' }}
                >
                  <option value="standard">🚛 Standard Delivery</option>
                  <option value="express">⚡ Express Delivery</option>
                  <option value="heavy">🏗️ Heavy Cargo</option>
                  <option value="refrigerated">❄️ Refrigerated Transport</option>
                  <option value="hazardous">⚠️ Hazardous Materials</option>
                </select>
                <label htmlFor="serviceType" className="fw-semibold">
                  <FaRoute className="me-2 text-primary" />
                  Primary Service Type *
                </label>
                {formik.touched.serviceType && formik.errors.serviceType && (
                  <div className="invalid-feedback">{formik.errors.serviceType}</div>
                )}
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="text"
                  className={`form-control ${formik.touched.gstNumber && formik.errors.gstNumber ? 'is-invalid' : ''}`}
                  id="gstNumber"
                  name="gstNumber"
                  value={formik.values.gstNumber}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="22AAAAA0000A1Z5"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="gstNumber" className="fw-semibold">
                  <FaShieldAlt className="me-2 text-warning" />
                  GST Number
                </label>
                {formik.touched.gstNumber && formik.errors.gstNumber && (
                  <div className="invalid-feedback">{formik.errors.gstNumber}</div>
                )}
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="text"
                  className={`form-control ${formik.touched.panNumber && formik.errors.panNumber ? 'is-invalid' : ''}`}
                  id="panNumber"
                  name="panNumber"
                  value={formik.values.panNumber}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="**********"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="panNumber" className="fw-semibold">
                  <FaIdCard className="me-2 text-info" />
                  PAN Number
                </label>
                {formik.touched.panNumber && formik.errors.panNumber && (
                  <div className="invalid-feedback">{formik.errors.panNumber}</div>
                )}
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="text"
                  className="form-control"
                  id="licenseNumber"
                  name="licenseNumber"
                  value={formik.values.licenseNumber}
                  onChange={formik.handleChange}
                  placeholder="TL123456789"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="licenseNumber" className="fw-semibold">
                  <FaCertificate className="me-2 text-warning" />
                  Transport License Number
                </label>
              </div>
            </div>

            <div className="col-md-6">
              <div className="form-floating">
                <input
                  type="text"
                  className="form-control"
                  id="insuranceNumber"
                  name="insuranceNumber"
                  value={formik.values.insuranceNumber}
                  onChange={formik.handleChange}
                  placeholder="INS123456789"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="insuranceNumber" className="fw-semibold">
                  <FaShieldAlt className="me-2 text-danger" />
                  Insurance Policy Number
                </label>
              </div>
            </div>

            <div className="col-12">
              <div className="form-floating">
                <input
                  type="url"
                  className={`form-control ${formik.touched.website && formik.errors.website ? 'is-invalid' : ''}`}
                  id="website"
                  name="website"
                  value={formik.values.website}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  placeholder="https://www.company.com"
                  style={{ borderRadius: '10px' }}
                />
                <label htmlFor="website" className="fw-semibold">
                  <FaGlobe className="me-2 text-primary" />
                  Company Website (Optional)
                </label>
                {formik.touched.website && formik.errors.website && (
                  <div className="invalid-feedback">{formik.errors.website}</div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className="col-lg-4">
          {/* Help Section */}
          <div className="card border-0 bg-light">
            <div className="card-body p-4">
              <h6 className="fw-bold text-success mb-3">
                <FaInfoCircle className="me-2" />
                Shipping Partner Guidelines
              </h6>
              <ul className="list-unstyled mb-0 small">
                <li className="mb-2">
                  <strong>Company Name:</strong> Legal business name as per transport license.
                </li>
                <li className="mb-2">
                  <strong>Service Type:</strong> Primary logistics service offered by the partner.
                </li>
                <li className="mb-2">
                  <strong>Transport License:</strong> Valid license number for commercial transport.
                </li>
                <li className="mb-2">
                  <strong>Insurance:</strong> Valid insurance policy for cargo protection.
                </li>
              </ul>
            </div>
          </div>

          {/* Service Type Info */}
          <div className="card border-0 bg-success bg-opacity-10 mt-3">
            <div className="card-body p-4">
              <h6 className="fw-bold text-success mb-3">Service Types</h6>
              <div className="small">
                <div className="mb-2">
                  <strong>🚛 Standard:</strong> Regular freight and cargo delivery
                </div>
                <div className="mb-2">
                  <strong>⚡ Express:</strong> Time-critical and urgent deliveries
                </div>
                <div className="mb-2">
                  <strong>🏗️ Heavy:</strong> Heavy machinery and oversized cargo
                </div>
                <div className="mb-2">
                  <strong>❄️ Refrigerated:</strong> Temperature-controlled transport
                </div>
                <div className="mb-2">
                  <strong>⚠️ Hazardous:</strong> Dangerous goods and chemicals
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CompanyDetailsStep;
