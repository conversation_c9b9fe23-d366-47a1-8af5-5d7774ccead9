import React, { useState, useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';
import DataTable from 'react-data-table-component';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaSearch,
  FaFilter,
  FaDownload,
  FaUsers,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope
} from 'react-icons/fa';
import ROUTES from '@constants/routes';

const Customers = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock data for customers
  const [customers] = useState([
    {
      id: 1,
      name: 'ABC Corporation',
      contactPerson: '<PERSON>',
      email: '<EMAIL>',
      phone: '+91 98765 43210',
      address: 'Mumbai, Maharashtra',
      status: 'active',
      totalShipments: 45,
      lastOrder: '2024-01-15',
      creditLimit: '₹5,00,000'
    },
    {
      id: 2,
      name: 'XYZ Industries',
      contactPerson: '<PERSON>',
      email: '<EMAIL>',
      phone: '+91 87654 32109',
      address: 'Delhi, NCR',
      status: 'active',
      totalShipments: 32,
      lastOrder: '2024-01-12',
      creditLimit: '₹3,00,000'
    },
    {
      id: 3,
      name: 'PQR Enterprises',
      contactPerson: 'Mike Wilson',
      email: '<EMAIL>',
      phone: '+91 76543 21098',
      address: 'Bangalore, Karnataka',
      status: 'inactive',
      totalShipments: 18,
      lastOrder: '2023-12-20',
      creditLimit: '₹2,00,000'
    },
    {
      id: 4,
      name: 'LMN Trading Co.',
      contactPerson: 'Emily Davis',
      email: '<EMAIL>',
      phone: '+91 65432 10987',
      address: 'Chennai, Tamil Nadu',
      status: 'active',
      totalShipments: 67,
      lastOrder: '2024-01-18',
      creditLimit: '₹7,50,000'
    }
  ]);

  // Filter customers based on search and status
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || customer.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status) => {
    return status === 'active'
      ? <span className="badge bg-success">Active</span>
      : <span className="badge bg-secondary">Inactive</span>;
  };

  // DataTable columns configuration
  const columns = useMemo(() => [
    {
      name: 'Customer',
      selector: row => row.name,
      sortable: true,
      minWidth: '200px',
      cell: row => (
        <div>
          <div className="fw-semibold text-dark">{row.name}</div>
          <small className="text-muted">{row.contactPerson}</small>
        </div>
      ),
    },
    {
      name: 'Contact Info',
      selector: row => row.email,
      sortable: true,
      minWidth: '220px',
      cell: row => (
        <div>
          <div className="d-flex align-items-center mb-1">
            <FaEnvelope className="text-muted me-2" size={12} />
            <small>{row.email}</small>
          </div>
          <div className="d-flex align-items-center">
            <FaPhone className="text-muted me-2" size={12} />
            <small>{row.phone}</small>
          </div>
        </div>
      ),
    },
    {
      name: 'Location',
      selector: row => row.address,
      sortable: true,
      minWidth: '150px',
      cell: row => (
        <div className="d-flex align-items-center">
          <FaMapMarkerAlt className="text-muted me-2" size={12} />
          <small>{row.address}</small>
        </div>
      ),
    },
    {
      name: 'Shipments',
      selector: row => row.totalShipments,
      sortable: true,
      minWidth: '120px',
      cell: row => (
        <div>
          <div className="fw-semibold">{row.totalShipments}</div>
          <small className="text-muted">Last: {row.lastOrder}</small>
        </div>
      ),
    },
    {
      name: 'Credit Limit',
      selector: row => row.creditLimit,
      sortable: true,
      minWidth: '120px',
      cell: row => (
        <span className="fw-semibold text-success">{row.creditLimit}</span>
      ),
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      minWidth: '100px',
      cell: row => getStatusBadge(row.status),
    },
    {
      name: 'Actions',
      minWidth: '120px',
      cell: row => (
        <div className="d-flex gap-1">
          <button className="btn btn-sm btn-outline-primary" title="View Details">
            <FaEye size={12} />
          </button>
          <button className="btn btn-sm btn-outline-warning" title="Edit Customer">
            <FaEdit size={12} />
          </button>
          <button className="btn btn-sm btn-outline-danger" title="Delete Customer">
            <FaTrash size={12} />
          </button>
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ], []);

  // Custom styles for DataTable
  const customStyles = {
    header: {
      style: {
        minHeight: '56px',
      },
    },
    headRow: {
      style: {
        borderTopStyle: 'solid',
        borderTopWidth: '1px',
        borderTopColor: '#e9ecef',
        backgroundColor: '#f8f9fa',
      },
    },
    headCells: {
      style: {
        '&:not(:last-of-type)': {
          borderRightStyle: 'solid',
          borderRightWidth: '1px',
          borderRightColor: '#e9ecef',
        },
        fontSize: '14px',
        fontWeight: '600',
        color: '#495057',
      },
    },
    cells: {
      style: {
        '&:not(:last-of-type)': {
          borderRightStyle: 'solid',
          borderRightWidth: '1px',
          borderRightColor: '#e9ecef',
        },
        fontSize: '13px',
        padding: '12px 8px',
      },
    },
    rows: {
      style: {
        minHeight: '60px',
        '&:hover': {
          backgroundColor: '#f8f9fa',
        },
      },
    },
  };

  return (
    <div className="customers-page">
      {/* Page Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="h3 mb-1 text-dark fw-bold">
                <FaUsers className="me-2 text-primary" />
                Customers Management
              </h1>
              <p className="text-muted mb-0">Manage your customer database and relationships</p>
            </div>
            <div className="d-flex gap-2">
              <button className="btn btn-outline-primary">
                <FaDownload className="me-2" />
                Export
              </button>
              <Link to={ROUTES.CUSTOMERS_CREATE} className="btn btn-primary">
                <FaPlus className="me-2" />
                Add Customer
              </Link>
            </div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="row g-3">
                <div className="col-md-6">
                  <div className="position-relative">
                    <FaSearch className="position-absolute top-50 start-0 translate-middle-y ms-3 text-muted" />
                    <input
                      type="text"
                      className="form-control ps-5"
                      placeholder="Search customers by name, contact person, or email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                    />
                  </div>
                </div>
                <div className="col-md-3">
                  <select
                    className="form-select"
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                  >
                    <option value="all">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                  </select>
                </div>
                <div className="col-md-3">
                  <button className="btn btn-outline-secondary w-100">
                    <FaFilter className="me-2" />
                    More Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="row mb-4">
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-primary bg-opacity-10 rounded-circle p-3">
                    <FaUsers className="text-primary" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{customers.length}</div>
                  <div className="text-muted small">Total Customers</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-success bg-opacity-10 rounded-circle p-3">
                    <FaUsers className="text-success" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{customers.filter(c => c.status === 'active').length}</div>
                  <div className="text-muted small">Active Customers</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-warning bg-opacity-10 rounded-circle p-3">
                    <FaUsers className="text-warning" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">{customers.reduce((sum, c) => sum + c.totalShipments, 0)}</div>
                  <div className="text-muted small">Total Shipments</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-sm">
            <div className="card-body">
              <div className="d-flex align-items-center">
                <div className="flex-shrink-0">
                  <div className="bg-info bg-opacity-10 rounded-circle p-3">
                    <FaUsers className="text-info" size={24} />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="h4 mb-0 fw-bold">₹18L</div>
                  <div className="text-muted small">Total Credit Limit</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Customers Table */}
      <div className="row">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-white border-bottom">
              <h5 className="mb-0 fw-semibold">
                Customers List ({filteredCustomers.length})
              </h5>
            </div>
            <div className="card-body p-0">
              <div className="logistics-table customers-table">
                <DataTable
                  columns={columns}
                  data={filteredCustomers}
                  pagination
                  paginationPerPage={10}
                  paginationRowsPerPageOptions={[5, 10, 15, 20]}
                  responsive
                  highlightOnHover
                  striped
                  customStyles={customStyles}
                  noDataComponent={
                    <div className="text-center py-4">
                      <FaUsers className="text-muted mb-2" size={48} />
                      <p className="text-muted">No customers found</p>
                    </div>
                  }
                  progressPending={false}
                  progressComponent={
                    <div className="text-center py-4">
                      <div className="spinner-border text-primary" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                    </div>
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Customers;
