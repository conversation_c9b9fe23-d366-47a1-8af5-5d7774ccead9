import React, { useState, useMemo } from 'react';
import { <PERSON> } from 'react-router-dom';
import DataTable from 'react-data-table-component';
import {
  FaPlus,
  FaEdit,
  FaTrash,
  FaEye,
  FaSearch,
  FaFilter,
  FaDownload,
  FaUsers,
  FaMapMarkerAlt,
  FaPhone,
  FaEnvelope
} from 'react-icons/fa';
import ROUTES from '@constants/routes';

const Customers = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('all');

  // Mock data for customers
  const [customers] = useState([
    {
      id: 1,
      name: 'ABC Corporation',
      contactPerson: '<PERSON>',
      email: '<EMAIL>',
      phone: '+91 98765 43210',
      address: 'Mumbai, Maharashtra',
      status: 'active',
      totalShipments: 45,
      lastOrder: '2024-01-15',
      creditLimit: '₹5,00,000'
    },
    {
      id: 2,
      name: 'XYZ Industries',
      contactPerson: '<PERSON>',
      email: '<EMAIL>',
      phone: '+91 87654 32109',
      address: 'Delhi, NCR',
      status: 'active',
      totalShipments: 32,
      lastOrder: '2024-01-12',
      creditLimit: '₹3,00,000'
    },
    {
      id: 3,
      name: 'PQR Enterprises',
      contactPerson: 'Mike Wilson',
      email: '<EMAIL>',
      phone: '+91 76543 21098',
      address: 'Bangalore, Karnataka',
      status: 'inactive',
      totalShipments: 18,
      lastOrder: '2023-12-20',
      creditLimit: '₹2,00,000'
    },
    {
      id: 4,
      name: 'LMN Trading Co.',
      contactPerson: 'Emily Davis',
      email: '<EMAIL>',
      phone: '+91 65432 10987',
      address: 'Chennai, Tamil Nadu',
      status: 'active',
      totalShipments: 67,
      lastOrder: '2024-01-18',
      creditLimit: '₹7,50,000'
    }
  ]);

  // Filter customers based on search and status
  const filteredCustomers = customers.filter(customer => {
    const matchesSearch = customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.contactPerson.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         customer.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = filterStatus === 'all' || customer.status === filterStatus;
    return matchesSearch && matchesStatus;
  });

  const getStatusBadge = (status) => {
    return status === 'active'
      ? <span className="badge bg-success">Active</span>
      : <span className="badge bg-secondary">Inactive</span>;
  };

  // DataTable columns configuration
  const columns = useMemo(() => [
    {
      name: 'Customer',
      selector: row => row.name,
      sortable: true,
      minWidth: '200px',
      cell: row => (
        <div>
          <div className="fw-semibold text-dark">{row.name}</div>
          <small className="text-muted">{row.contactPerson}</small>
        </div>
      ),
    },
    {
      name: 'Contact Info',
      selector: row => row.email,
      sortable: true,
      minWidth: '220px',
      cell: row => (
        <div>
          <div className="d-flex align-items-center mb-1">
            <FaEnvelope className="text-muted me-2" size={12} />
            <small>{row.email}</small>
          </div>
          <div className="d-flex align-items-center">
            <FaPhone className="text-muted me-2" size={12} />
            <small>{row.phone}</small>
          </div>
        </div>
      ),
    },
    {
      name: 'Location',
      selector: row => row.address,
      sortable: true,
      minWidth: '150px',
      cell: row => (
        <div className="d-flex align-items-center">
          <FaMapMarkerAlt className="text-muted me-2" size={12} />
          <small>{row.address}</small>
        </div>
      ),
    },
    {
      name: 'Shipments',
      selector: row => row.totalShipments,
      sortable: true,
      minWidth: '120px',
      cell: row => (
        <div>
          <div className="fw-semibold">{row.totalShipments}</div>
          <small className="text-muted">Last: {row.lastOrder}</small>
        </div>
      ),
    },
    {
      name: 'Credit Limit',
      selector: row => row.creditLimit,
      sortable: true,
      minWidth: '120px',
      cell: row => (
        <span className="fw-semibold text-success">{row.creditLimit}</span>
      ),
    },
    {
      name: 'Status',
      selector: row => row.status,
      sortable: true,
      minWidth: '100px',
      cell: row => getStatusBadge(row.status),
    },
    {
      name: 'Actions',
      minWidth: '120px',
      cell: row => (
        <div className="d-flex gap-1">
          <button className="btn btn-sm btn-outline-primary" title="View Details">
            <FaEye size={12} />
          </button>
          <button className="btn btn-sm btn-outline-warning" title="Edit Customer">
            <FaEdit size={12} />
          </button>
          <button className="btn btn-sm btn-outline-danger" title="Delete Customer">
            <FaTrash size={12} />
          </button>
        </div>
      ),
      ignoreRowClick: true,
      allowOverflow: true,
      button: true,
    },
  ], []);

  // Custom styles for DataTable
  const customStyles = {
    header: {
      style: {
        minHeight: '56px',
      },
    },
    headRow: {
      style: {
        borderTopStyle: 'solid',
        borderTopWidth: '1px',
        borderTopColor: '#e9ecef',
        backgroundColor: '#f8f9fa',
      },
    },
    headCells: {
      style: {
        '&:not(:last-of-type)': {
          borderRightStyle: 'solid',
          borderRightWidth: '1px',
          borderRightColor: '#e9ecef',
        },
        fontSize: '14px',
        fontWeight: '600',
        color: '#495057',
      },
    },
    cells: {
      style: {
        '&:not(:last-of-type)': {
          borderRightStyle: 'solid',
          borderRightWidth: '1px',
          borderRightColor: '#e9ecef',
        },
        fontSize: '13px',
        padding: '12px 8px',
      },
    },
    rows: {
      style: {
        minHeight: '60px',
        '&:hover': {
          backgroundColor: '#f8f9fa',
        },
      },
    },
  };

  return (
    <div className="customers-page">
      {/* Modern Page Header with Gradient Background */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-lg" style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '20px'
          }}>
            <div className="card-body p-4">
              <div className="d-flex justify-content-between align-items-center text-white">
                <div className="d-flex align-items-center">
                  <div className="bg-white bg-opacity-20 rounded-circle p-3 me-4">
                    <FaUsers className="text-white" size={32} />
                  </div>
                  <div>
                    <h1 className="h2 mb-2 text-white fw-bold">
                      Customer Management Hub
                    </h1>
                    <p className="text-white-50 mb-0 fs-5">
                      Streamline your customer relationships and logistics partnerships
                    </p>
                  </div>
                </div>
                <div className="d-flex gap-3">
                  <button className="btn btn-light btn-lg rounded-pill px-4">
                    <FaDownload className="me-2" />
                    Export Data
                  </button>
                  <div className="dropdown">
                    <button className="btn btn-warning btn-lg rounded-pill px-4 fw-bold dropdown-toggle" type="button" data-bs-toggle="dropdown">
                      <FaPlus className="me-2" />
                      Add New Customer
                    </button>
                    <ul className="dropdown-menu">
                      <li>
                        <Link to={ROUTES.CUSTOMERS_CREATE_FLOW} className="dropdown-item">
                          <FaPlus className="me-2" />
                          Multi-Step Form (Recommended)
                        </Link>
                      </li>
                      <li>
                        <Link to={ROUTES.CUSTOMERS_CREATE} className="dropdown-item">
                          <FaPlus className="me-2" />
                          Single Page Form
                        </Link>
                      </li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Search and Filters */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-lg" style={{ borderRadius: '15px' }}>
            <div className="card-body p-4">
              <div className="row g-4">
                <div className="col-lg-6">
                  <div className="position-relative">
                    <div className="input-group input-group-lg">
                      <span className="input-group-text bg-primary text-white border-0" style={{ borderRadius: '12px 0 0 12px' }}>
                        <FaSearch size={18} />
                      </span>
                      <input
                        type="text"
                        className="form-control border-0 shadow-sm"
                        placeholder="Search customers by name, contact person, or email..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        style={{
                          borderRadius: '0 12px 12px 0',
                          fontSize: '16px',
                          padding: '12px 16px'
                        }}
                      />
                    </div>
                  </div>
                </div>
                <div className="col-lg-3">
                  <select
                    className="form-select form-select-lg border-0 shadow-sm"
                    value={filterStatus}
                    onChange={(e) => setFilterStatus(e.target.value)}
                    style={{
                      borderRadius: '12px',
                      fontSize: '16px',
                      padding: '12px 16px'
                    }}
                  >
                    <option value="all">🔍 All Status</option>
                    <option value="active">✅ Active Customers</option>
                    <option value="inactive">⏸️ Inactive Customers</option>
                  </select>
                </div>
                <div className="col-lg-3">
                  <button className="btn btn-outline-primary btn-lg w-100 fw-semibold" style={{ borderRadius: '12px' }}>
                    <FaFilter className="me-2" />
                    Advanced Filters
                  </button>
                </div>
              </div>

              {/* Quick Filter Tags */}
              <div className="mt-3">
                <div className="d-flex flex-wrap gap-2">
                  <span className="badge bg-light text-dark px-3 py-2 rounded-pill">
                    <FaUsers className="me-1" />
                    All Customers ({customers.length})
                  </span>
                  <span className="badge bg-success-subtle text-success px-3 py-2 rounded-pill">
                    <FaUsers className="me-1" />
                    Active ({customers.filter(c => c.status === 'active').length})
                  </span>
                  <span className="badge bg-warning-subtle text-warning px-3 py-2 rounded-pill">
                    <FaUsers className="me-1" />
                    High Value (₹5L+)
                  </span>
                  <span className="badge bg-info-subtle text-info px-3 py-2 rounded-pill">
                    <FaUsers className="me-1" />
                    Recent Orders
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Statistics Cards */}
      <div className="row mb-4">
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-lg h-100" style={{
            borderRadius: '15px',
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
          }}>
            <div className="card-body p-4 text-white">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <div className="h2 mb-1 fw-bold">{customers.length}</div>
                  <div className="text-white-50 fw-semibold">Total Customers</div>
                  <div className="mt-2">
                    <small className="text-white-50">📈 +12% this month</small>
                  </div>
                </div>
                <div className="bg-white bg-opacity-20 rounded-circle p-3">
                  <FaUsers className="text-white" size={28} />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-lg h-100" style={{
            borderRadius: '15px',
            background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)'
          }}>
            <div className="card-body p-4 text-white">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <div className="h2 mb-1 fw-bold">{customers.filter(c => c.status === 'active').length}</div>
                  <div className="text-white-50 fw-semibold">Active Customers</div>
                  <div className="mt-2">
                    <small className="text-white-50">✅ {Math.round((customers.filter(c => c.status === 'active').length / customers.length) * 100)}% active rate</small>
                  </div>
                </div>
                <div className="bg-white bg-opacity-20 rounded-circle p-3">
                  <FaUsers className="text-white" size={28} />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-lg h-100" style={{
            borderRadius: '15px',
            background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)'
          }}>
            <div className="card-body p-4 text-white">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <div className="h2 mb-1 fw-bold">{customers.reduce((sum, c) => sum + c.totalShipments, 0)}</div>
                  <div className="text-white-50 fw-semibold">Total Shipments</div>
                  <div className="mt-2">
                    <small className="text-white-50">📦 Avg: {Math.round(customers.reduce((sum, c) => sum + c.totalShipments, 0) / customers.length)} per customer</small>
                  </div>
                </div>
                <div className="bg-white bg-opacity-20 rounded-circle p-3">
                  <FaUsers className="text-white" size={28} />
                </div>
              </div>
            </div>
          </div>
        </div>
        <div className="col-xl-3 col-md-6 mb-3">
          <div className="card border-0 shadow-lg h-100" style={{
            borderRadius: '15px',
            background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)'
          }}>
            <div className="card-body p-4 text-white">
              <div className="d-flex align-items-center justify-content-between">
                <div>
                  <div className="h2 mb-1 fw-bold">₹18L</div>
                  <div className="text-white-50 fw-semibold">Total Credit Limit</div>
                  <div className="mt-2">
                    <small className="text-white-50">💰 Avg: ₹4.5L per customer</small>
                  </div>
                </div>
                <div className="bg-white bg-opacity-20 rounded-circle p-3">
                  <FaUsers className="text-white" size={28} />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Enhanced Customers Table */}
      <div className="row">
        <div className="col-12">
          <div className="card border-0 shadow-lg" style={{ borderRadius: '15px' }}>
            <div className="card-header border-0" style={{
              background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
              borderRadius: '15px 15px 0 0'
            }}>
              <div className="d-flex justify-content-between align-items-center text-white py-2">
                <div className="d-flex align-items-center">
                  <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                    <FaUsers className="text-white" size={20} />
                  </div>
                  <h5 className="mb-0 fw-bold text-white">
                    Customer Database ({filteredCustomers.length} records)
                  </h5>
                </div>
                <div className="d-flex gap-2">
                  <span className="badge bg-white bg-opacity-20 text-white px-3 py-2 rounded-pill">
                    📊 Live Data
                  </span>
                  <span className="badge bg-success bg-opacity-20 text-white px-3 py-2 rounded-pill">
                    ✅ Real-time Updates
                  </span>
                </div>
              </div>
            </div>
            <div className="card-body p-0">
              <div className="logistics-table customers-table">
                <DataTable
                  columns={columns}
                  data={filteredCustomers}
                  pagination
                  paginationPerPage={10}
                  paginationRowsPerPageOptions={[5, 10, 15, 20]}
                  responsive
                  highlightOnHover
                  striped
                  customStyles={customStyles}
                  noDataComponent={
                    <div className="text-center py-5">
                      <div className="mb-3">
                        <FaUsers className="text-muted" size={64} />
                      </div>
                      <h5 className="text-muted mb-2">No customers found</h5>
                      <p className="text-muted mb-3">Try adjusting your search criteria or add a new customer</p>
                      <Link to={ROUTES.CUSTOMERS_CREATE} className="btn btn-primary rounded-pill px-4">
                        <FaPlus className="me-2" />
                        Add First Customer
                      </Link>
                    </div>
                  }
                  progressPending={false}
                  progressComponent={
                    <div className="text-center py-5">
                      <div className="spinner-border text-primary mb-3" role="status" style={{ width: '3rem', height: '3rem' }}>
                        <span className="visually-hidden">Loading...</span>
                      </div>
                      <h5 className="text-primary">Loading customer data...</h5>
                      <p className="text-muted">Please wait while we fetch the latest information</p>
                    </div>
                  }
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Customers;
