import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import toast from 'react-hot-toast';
import {
  FaArrowLeft,
  FaSave,
  FaUser,
  FaBuilding,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaRupeeSign,
  FaFileAlt,
  FaGlobe
} from 'react-icons/fa';
import ROUTES from '@constants/routes';

const CreateCustomer = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation schema
  const validationSchema = Yup.object({
    companyName: Yup.string()
      .required('Company name is required')
      .min(2, 'Company name must be at least 2 characters'),
    contactPerson: Yup.string()
      .required('Contact person name is required')
      .min(2, 'Contact person name must be at least 2 characters'),
    email: Yup.string()
      .email('Please enter a valid email address')
      .required('Email is required'),
    phone: Yup.string()
      .required('Phone number is required')
      .matches(/^[+]?[\d\s-()]+$/, 'Please enter a valid phone number'),
    alternatePhone: Yup.string()
      .matches(/^[+]?[\d\s-()]+$/, 'Please enter a valid phone number'),
    address: Yup.string()
      .required('Address is required')
      .min(10, 'Address must be at least 10 characters'),
    city: Yup.string()
      .required('City is required'),
    state: Yup.string()
      .required('State is required'),
    pincode: Yup.string()
      .required('Pincode is required')
      .matches(/^\d{6}$/, 'Pincode must be 6 digits'),
    gstNumber: Yup.string()
      .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Please enter a valid GST number'),
    creditLimit: Yup.number()
      .min(0, 'Credit limit cannot be negative')
      .required('Credit limit is required'),
    paymentTerms: Yup.string()
      .required('Payment terms are required'),
  });

  // Form handling
  const formik = useFormik({
    initialValues: {
      companyName: '',
      contactPerson: '',
      designation: '',
      email: '',
      phone: '',
      alternatePhone: '',
      address: '',
      city: '',
      state: '',
      pincode: '',
      country: 'India',
      gstNumber: '',
      panNumber: '',
      creditLimit: '',
      paymentTerms: '30',
      customerType: 'regular',
      notes: '',
      website: ''
    },
    validationSchema,
    onSubmit: async (values) => {
      setIsSubmitting(true);
      try {
        // Simulate API call
        console.log('Creating customer:', values);
        await new Promise(resolve => setTimeout(resolve, 1500));
        
        toast.success('Customer created successfully!');
        navigate(ROUTES.CUSTOMERS);
      } catch (error) {
        toast.error('Failed to create customer. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  return (
    <div className="create-customer-page">
      {/* Page Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex align-items-center">
            <Link to={ROUTES.CUSTOMERS} className="btn btn-outline-secondary me-3">
              <FaArrowLeft className="me-2" />
              Back
            </Link>
            <div>
              <h1 className="h3 mb-1 text-dark fw-bold">
                <FaUser className="me-2 text-primary" />
                Add New Customer
              </h1>
              <p className="text-muted mb-0">Create a new customer profile for your logistics operations</p>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <div className="row">
          {/* Main Form */}
          <div className="col-lg-8">
            {/* Company Information */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaBuilding className="me-2 text-primary" />
                  Company Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Company Name *</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.companyName && formik.errors.companyName ? 'is-invalid' : ''}`}
                      name="companyName"
                      value={formik.values.companyName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter company name"
                    />
                    {formik.touched.companyName && formik.errors.companyName && (
                      <div className="invalid-feedback">{formik.errors.companyName}</div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Customer Type</label>
                    <select
                      className="form-select"
                      name="customerType"
                      value={formik.values.customerType}
                      onChange={formik.handleChange}
                    >
                      <option value="regular">Regular</option>
                      <option value="premium">Premium</option>
                      <option value="enterprise">Enterprise</option>
                    </select>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">GST Number</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.gstNumber && formik.errors.gstNumber ? 'is-invalid' : ''}`}
                      name="gstNumber"
                      value={formik.values.gstNumber}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="22AAAAA0000A1Z5"
                    />
                    {formik.touched.gstNumber && formik.errors.gstNumber && (
                      <div className="invalid-feedback">{formik.errors.gstNumber}</div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">PAN Number</label>
                    <input
                      type="text"
                      className="form-control"
                      name="panNumber"
                      value={formik.values.panNumber}
                      onChange={formik.handleChange}
                      placeholder="**********"
                    />
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">Website</label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaGlobe />
                      </span>
                      <input
                        type="url"
                        className="form-control"
                        name="website"
                        value={formik.values.website}
                        onChange={formik.handleChange}
                        placeholder="https://www.company.com"
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaUser className="me-2 text-primary" />
                  Contact Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Contact Person *</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.contactPerson && formik.errors.contactPerson ? 'is-invalid' : ''}`}
                      name="contactPerson"
                      value={formik.values.contactPerson}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter contact person name"
                    />
                    {formik.touched.contactPerson && formik.errors.contactPerson && (
                      <div className="invalid-feedback">{formik.errors.contactPerson}</div>
                    )}
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Designation</label>
                    <input
                      type="text"
                      className="form-control"
                      name="designation"
                      value={formik.values.designation}
                      onChange={formik.handleChange}
                      placeholder="Manager, Director, etc."
                    />
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Email Address *</label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaEnvelope />
                      </span>
                      <input
                        type="email"
                        className={`form-control ${formik.touched.email && formik.errors.email ? 'is-invalid' : ''}`}
                        name="email"
                        value={formik.values.email}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="<EMAIL>"
                      />
                      {formik.touched.email && formik.errors.email && (
                        <div className="invalid-feedback">{formik.errors.email}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Phone Number *</label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaPhone />
                      </span>
                      <input
                        type="tel"
                        className={`form-control ${formik.touched.phone && formik.errors.phone ? 'is-invalid' : ''}`}
                        name="phone"
                        value={formik.values.phone}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="+91 98765 43210"
                      />
                      {formik.touched.phone && formik.errors.phone && (
                        <div className="invalid-feedback">{formik.errors.phone}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <label className="form-label fw-semibold">Alternate Phone</label>
                    <div className="input-group">
                      <span className="input-group-text">
                        <FaPhone />
                      </span>
                      <input
                        type="tel"
                        className={`form-control ${formik.touched.alternatePhone && formik.errors.alternatePhone ? 'is-invalid' : ''}`}
                        name="alternatePhone"
                        value={formik.values.alternatePhone}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="+91 87654 32109"
                      />
                      {formik.touched.alternatePhone && formik.errors.alternatePhone && (
                        <div className="invalid-feedback">{formik.errors.alternatePhone}</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="col-lg-4">
            {/* Address Information */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaMapMarkerAlt className="me-2 text-primary" />
                  Address Information
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-12">
                    <label className="form-label fw-semibold">Address *</label>
                    <textarea
                      className={`form-control ${formik.touched.address && formik.errors.address ? 'is-invalid' : ''}`}
                      name="address"
                      rows="3"
                      value={formik.values.address}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter complete address"
                    />
                    {formik.touched.address && formik.errors.address && (
                      <div className="invalid-feedback">{formik.errors.address}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">City *</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.city && formik.errors.city ? 'is-invalid' : ''}`}
                      name="city"
                      value={formik.values.city}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="Enter city"
                    />
                    {formik.touched.city && formik.errors.city && (
                      <div className="invalid-feedback">{formik.errors.city}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">State *</label>
                    <select
                      className={`form-select ${formik.touched.state && formik.errors.state ? 'is-invalid' : ''}`}
                      name="state"
                      value={formik.values.state}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    >
                      <option value="">Select State</option>
                      <option value="Maharashtra">Maharashtra</option>
                      <option value="Delhi">Delhi</option>
                      <option value="Karnataka">Karnataka</option>
                      <option value="Tamil Nadu">Tamil Nadu</option>
                      <option value="Gujarat">Gujarat</option>
                      <option value="Rajasthan">Rajasthan</option>
                      <option value="West Bengal">West Bengal</option>
                      <option value="Uttar Pradesh">Uttar Pradesh</option>
                    </select>
                    {formik.touched.state && formik.errors.state && (
                      <div className="invalid-feedback">{formik.errors.state}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">Pincode *</label>
                    <input
                      type="text"
                      className={`form-control ${formik.touched.pincode && formik.errors.pincode ? 'is-invalid' : ''}`}
                      name="pincode"
                      value={formik.values.pincode}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      placeholder="400001"
                    />
                    {formik.touched.pincode && formik.errors.pincode && (
                      <div className="invalid-feedback">{formik.errors.pincode}</div>
                    )}
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">Country</label>
                    <input
                      type="text"
                      className="form-control"
                      name="country"
                      value={formik.values.country}
                      onChange={formik.handleChange}
                      readOnly
                    />
                  </div>
                </div>
              </div>
            </div>

            {/* Business Terms */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaRupeeSign className="me-2 text-primary" />
                  Business Terms
                </h5>
              </div>
              <div className="card-body">
                <div className="row g-3">
                  <div className="col-12">
                    <label className="form-label fw-semibold">Credit Limit *</label>
                    <div className="input-group">
                      <span className="input-group-text">₹</span>
                      <input
                        type="number"
                        className={`form-control ${formik.touched.creditLimit && formik.errors.creditLimit ? 'is-invalid' : ''}`}
                        name="creditLimit"
                        value={formik.values.creditLimit}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="500000"
                      />
                      {formik.touched.creditLimit && formik.errors.creditLimit && (
                        <div className="invalid-feedback">{formik.errors.creditLimit}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-12">
                    <label className="form-label fw-semibold">Payment Terms *</label>
                    <select
                      className={`form-select ${formik.touched.paymentTerms && formik.errors.paymentTerms ? 'is-invalid' : ''}`}
                      name="paymentTerms"
                      value={formik.values.paymentTerms}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                    >
                      <option value="15">15 Days</option>
                      <option value="30">30 Days</option>
                      <option value="45">45 Days</option>
                      <option value="60">60 Days</option>
                      <option value="90">90 Days</option>
                    </select>
                    {formik.touched.paymentTerms && formik.errors.paymentTerms && (
                      <div className="invalid-feedback">{formik.errors.paymentTerms}</div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Notes */}
            <div className="card border-0 shadow-sm mb-4">
              <div className="card-header bg-white border-bottom">
                <h5 className="mb-0 fw-semibold">
                  <FaFileAlt className="me-2 text-primary" />
                  Additional Notes
                </h5>
              </div>
              <div className="card-body">
                <textarea
                  className="form-control"
                  name="notes"
                  rows="4"
                  value={formik.values.notes}
                  onChange={formik.handleChange}
                  placeholder="Any additional notes or special instructions..."
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="card border-0 shadow-sm">
              <div className="card-body">
                <div className="d-grid gap-2">
                  <button
                    type="submit"
                    className="btn btn-primary"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="spinner-border spinner-border-sm me-2" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <FaSave className="me-2" />
                        Create Customer
                      </>
                    )}
                  </button>
                  <Link to={ROUTES.CUSTOMERS} className="btn btn-outline-secondary">
                    Cancel
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CreateCustomer;
