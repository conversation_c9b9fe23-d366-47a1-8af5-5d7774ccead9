import React, { useState } from 'react';
import { <PERSON>, useNavigate } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import toast from 'react-hot-toast';
import {
  FaArrowLeft,
  FaSave,
  FaUser,
  FaBuilding,
  FaEnvelope,
  FaPhone,
  FaMapMarkerAlt,
  FaRupeeSign,
  FaFileAlt,
  FaGlobe,
  FaCheckCircle,
  FaIdCard,
  FaCreditCard,
  FaCalendarAlt,
  FaUserTie,
  FaIndustry,
  FaShieldAlt,
  FaChartLine
} from 'react-icons/fa';
import ROUTES from '@constants/routes';

const CreateCustomer = () => {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Validation schema
  const validationSchema = Yup.object({
    companyName: Yup.string()
      .required('Company name is required')
      .min(2, 'Company name must be at least 2 characters'),
    contactPerson: Yup.string()
      .required('Contact person name is required')
      .min(2, 'Contact person name must be at least 2 characters'),
    email: Yup.string()
      .email('Please enter a valid email address')
      .required('Email is required'),
    phone: Yup.string()
      .required('Phone number is required')
      .matches(/^[+]?[\d\s-()]+$/, 'Please enter a valid phone number'),
    alternatePhone: Yup.string()
      .matches(/^[+]?[\d\s-()]+$/, 'Please enter a valid phone number'),
    address: Yup.string()
      .required('Address is required')
      .min(10, 'Address must be at least 10 characters'),
    city: Yup.string()
      .required('City is required'),
    state: Yup.string()
      .required('State is required'),
    pincode: Yup.string()
      .required('Pincode is required')
      .matches(/^\d{6}$/, 'Pincode must be 6 digits'),
    gstNumber: Yup.string()
      .matches(/^[0-9]{2}[A-Z]{5}[0-9]{4}[A-Z]{1}[1-9A-Z]{1}Z[0-9A-Z]{1}$/, 'Please enter a valid GST number'),
    creditLimit: Yup.number()
      .min(0, 'Credit limit cannot be negative')
      .required('Credit limit is required'),
    paymentTerms: Yup.string()
      .required('Payment terms are required'),
  });

  // Form handling
  const formik = useFormik({
    initialValues: {
      companyName: '',
      contactPerson: '',
      designation: '',
      email: '',
      phone: '',
      alternatePhone: '',
      address: '',
      city: '',
      state: '',
      pincode: '',
      country: 'India',
      gstNumber: '',
      panNumber: '',
      creditLimit: '',
      paymentTerms: '30',
      customerType: 'regular',
      notes: '',
      website: ''
    },
    validationSchema,
    onSubmit: async (values) => {
      setIsSubmitting(true);
      try {
        // Simulate API call
        console.log('Creating customer:', values);
        await new Promise(resolve => setTimeout(resolve, 1500));

        toast.success('Customer created successfully!');
        navigate(ROUTES.CUSTOMERS);
      } catch (error) {
        toast.error('Failed to create customer. Please try again.');
      } finally {
        setIsSubmitting(false);
      }
    },
  });

  return (
    <div className="create-customer-page">
      {/* Modern Page Header with Gradient Background */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-lg" style={{
            background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
            borderRadius: '20px'
          }}>
            <div className="card-body p-4">
              <div className="d-flex align-items-center text-white">
                <Link to={ROUTES.CUSTOMERS} className="btn btn-light btn-sm me-3 rounded-pill">
                  <FaArrowLeft className="me-2" />
                  Back to Customers
                </Link>
                <div className="flex-grow-1">
                  <h1 className="h2 mb-2 text-white fw-bold d-flex align-items-center">
                    <div className="bg-white bg-opacity-20 rounded-circle p-3 me-3">
                      <FaUserTie className="text-white" size={24} />
                    </div>
                    Add New Customer
                  </h1>
                  <p className="text-white-50 mb-0 fs-5">
                    Create a comprehensive customer profile for seamless logistics management
                  </p>
                </div>
                <div className="text-end">
                  <div className="bg-white bg-opacity-20 rounded-circle p-3">
                    <FaBuilding className="text-white" size={32} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Progress Steps */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-body py-3">
              <div className="d-flex justify-content-between align-items-center">
                <div className="d-flex align-items-center">
                  <div className="bg-primary rounded-circle p-2 me-3">
                    <FaBuilding className="text-white" size={16} />
                  </div>
                  <span className="fw-semibold text-primary">Company Details</span>
                </div>
                <div className="border-top flex-grow-1 mx-3"></div>
                <div className="d-flex align-items-center">
                  <div className="bg-light rounded-circle p-2 me-3">
                    <FaUser className="text-muted" size={16} />
                  </div>
                  <span className="text-muted">Contact Info</span>
                </div>
                <div className="border-top flex-grow-1 mx-3"></div>
                <div className="d-flex align-items-center">
                  <div className="bg-light rounded-circle p-2 me-3">
                    <FaMapMarkerAlt className="text-muted" size={16} />
                  </div>
                  <span className="text-muted">Address & Terms</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <div className="row">
          {/* Main Form */}
          <div className="col-lg-8">
            {/* Company Information */}
            <div className="card border-0 shadow-lg mb-4" style={{ borderRadius: '15px' }}>
              <div className="card-header border-0" style={{
                background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                borderRadius: '15px 15px 0 0'
              }}>
                <div className="d-flex align-items-center text-white py-2">
                  <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                    <FaBuilding className="text-white" size={20} />
                  </div>
                  <h5 className="mb-0 fw-bold text-white">Company Information</h5>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-4">
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className={`form-control ${formik.touched.companyName && formik.errors.companyName ? 'is-invalid' : ''}`}
                        id="companyName"
                        name="companyName"
                        value={formik.values.companyName}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter company name"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="companyName" className="fw-semibold">
                        <FaBuilding className="me-2 text-primary" />
                        Company Name *
                      </label>
                      {formik.touched.companyName && formik.errors.companyName && (
                        <div className="invalid-feedback">{formik.errors.companyName}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <select
                        className="form-select"
                        id="customerType"
                        name="customerType"
                        value={formik.values.customerType}
                        onChange={formik.handleChange}
                        style={{ borderRadius: '10px' }}
                      >
                        <option value="regular">Regular Customer</option>
                        <option value="premium">Premium Customer</option>
                        <option value="enterprise">Enterprise Customer</option>
                      </select>
                      <label htmlFor="customerType" className="fw-semibold">
                        <FaChartLine className="me-2 text-success" />
                        Customer Type
                      </label>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className={`form-control ${formik.touched.gstNumber && formik.errors.gstNumber ? 'is-invalid' : ''}`}
                        id="gstNumber"
                        name="gstNumber"
                        value={formik.values.gstNumber}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="22AAAAA0000A1Z5"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="gstNumber" className="fw-semibold">
                        <FaShieldAlt className="me-2 text-warning" />
                        GST Number
                      </label>
                      {formik.touched.gstNumber && formik.errors.gstNumber && (
                        <div className="invalid-feedback">{formik.errors.gstNumber}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className="form-control"
                        id="panNumber"
                        name="panNumber"
                        value={formik.values.panNumber}
                        onChange={formik.handleChange}
                        placeholder="**********"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="panNumber" className="fw-semibold">
                        <FaIdCard className="me-2 text-info" />
                        PAN Number
                      </label>
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="form-floating">
                      <input
                        type="url"
                        className="form-control"
                        id="website"
                        name="website"
                        value={formik.values.website}
                        onChange={formik.handleChange}
                        placeholder="https://www.company.com"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="website" className="fw-semibold">
                        <FaGlobe className="me-2 text-primary" />
                        Company Website
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Contact Information */}
            <div className="card border-0 shadow-lg mb-4" style={{ borderRadius: '15px' }}>
              <div className="card-header border-0" style={{
                background: 'linear-gradient(135deg, #11998e 0%, #38ef7d 100%)',
                borderRadius: '15px 15px 0 0'
              }}>
                <div className="d-flex align-items-center text-white py-2">
                  <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                    <FaUser className="text-white" size={20} />
                  </div>
                  <h5 className="mb-0 fw-bold text-white">Contact Information</h5>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-4">
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className={`form-control ${formik.touched.contactPerson && formik.errors.contactPerson ? 'is-invalid' : ''}`}
                        id="contactPerson"
                        name="contactPerson"
                        value={formik.values.contactPerson}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter contact person name"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="contactPerson" className="fw-semibold">
                        <FaUser className="me-2 text-primary" />
                        Contact Person *
                      </label>
                      {formik.touched.contactPerson && formik.errors.contactPerson && (
                        <div className="invalid-feedback">{formik.errors.contactPerson}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="text"
                        className="form-control"
                        id="designation"
                        name="designation"
                        value={formik.values.designation}
                        onChange={formik.handleChange}
                        placeholder="Manager, Director, etc."
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="designation" className="fw-semibold">
                        <FaUserTie className="me-2 text-success" />
                        Designation
                      </label>
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="email"
                        className={`form-control ${formik.touched.email && formik.errors.email ? 'is-invalid' : ''}`}
                        id="email"
                        name="email"
                        value={formik.values.email}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="<EMAIL>"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="email" className="fw-semibold">
                        <FaEnvelope className="me-2 text-info" />
                        Email Address *
                      </label>
                      {formik.touched.email && formik.errors.email && (
                        <div className="invalid-feedback">{formik.errors.email}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="tel"
                        className={`form-control ${formik.touched.phone && formik.errors.phone ? 'is-invalid' : ''}`}
                        id="phone"
                        name="phone"
                        value={formik.values.phone}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="+91 98765 43210"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="phone" className="fw-semibold">
                        <FaPhone className="me-2 text-warning" />
                        Phone Number *
                      </label>
                      {formik.touched.phone && formik.errors.phone && (
                        <div className="invalid-feedback">{formik.errors.phone}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-md-6">
                    <div className="form-floating">
                      <input
                        type="tel"
                        className={`form-control ${formik.touched.alternatePhone && formik.errors.alternatePhone ? 'is-invalid' : ''}`}
                        id="alternatePhone"
                        name="alternatePhone"
                        value={formik.values.alternatePhone}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="+91 87654 32109"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="alternatePhone" className="fw-semibold">
                        <FaPhone className="me-2 text-secondary" />
                        Alternate Phone
                      </label>
                      {formik.touched.alternatePhone && formik.errors.alternatePhone && (
                        <div className="invalid-feedback">{formik.errors.alternatePhone}</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="col-lg-4">
            {/* Address Information */}
            <div className="card border-0 shadow-lg mb-4" style={{ borderRadius: '15px' }}>
              <div className="card-header border-0" style={{
                background: 'linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)',
                borderRadius: '15px 15px 0 0'
              }}>
                <div className="d-flex align-items-center text-white py-2">
                  <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                    <FaMapMarkerAlt className="text-white" size={20} />
                  </div>
                  <h5 className="mb-0 fw-bold text-white">Address Information</h5>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-4">
                  <div className="col-12">
                    <div className="form-floating">
                      <textarea
                        className={`form-control ${formik.touched.address && formik.errors.address ? 'is-invalid' : ''}`}
                        id="address"
                        name="address"
                        rows="3"
                        value={formik.values.address}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter complete address"
                        style={{ borderRadius: '10px', minHeight: '100px' }}
                      />
                      <label htmlFor="address" className="fw-semibold">
                        <FaMapMarkerAlt className="me-2 text-danger" />
                        Complete Address *
                      </label>
                      {formik.touched.address && formik.errors.address && (
                        <div className="invalid-feedback">{formik.errors.address}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="form-floating">
                      <input
                        type="text"
                        className={`form-control ${formik.touched.city && formik.errors.city ? 'is-invalid' : ''}`}
                        id="city"
                        name="city"
                        value={formik.values.city}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="Enter city"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="city" className="fw-semibold">
                        <FaBuilding className="me-2 text-primary" />
                        City *
                      </label>
                      {formik.touched.city && formik.errors.city && (
                        <div className="invalid-feedback">{formik.errors.city}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="form-floating">
                      <select
                        className={`form-select ${formik.touched.state && formik.errors.state ? 'is-invalid' : ''}`}
                        id="state"
                        name="state"
                        value={formik.values.state}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        style={{ borderRadius: '10px' }}
                      >
                        <option value="">Select State</option>
                        <option value="Maharashtra">Maharashtra</option>
                        <option value="Delhi">Delhi</option>
                        <option value="Karnataka">Karnataka</option>
                        <option value="Tamil Nadu">Tamil Nadu</option>
                        <option value="Gujarat">Gujarat</option>
                        <option value="Rajasthan">Rajasthan</option>
                        <option value="West Bengal">West Bengal</option>
                        <option value="Uttar Pradesh">Uttar Pradesh</option>
                      </select>
                      <label htmlFor="state" className="fw-semibold">
                        <FaMapMarkerAlt className="me-2 text-success" />
                        State *
                      </label>
                      {formik.touched.state && formik.errors.state && (
                        <div className="invalid-feedback">{formik.errors.state}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="form-floating">
                      <input
                        type="text"
                        className={`form-control ${formik.touched.pincode && formik.errors.pincode ? 'is-invalid' : ''}`}
                        id="pincode"
                        name="pincode"
                        value={formik.values.pincode}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="400001"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="pincode" className="fw-semibold">
                        <FaMapMarkerAlt className="me-2 text-warning" />
                        Pincode *
                      </label>
                      {formik.touched.pincode && formik.errors.pincode && (
                        <div className="invalid-feedback">{formik.errors.pincode}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="form-floating">
                      <input
                        type="text"
                        className="form-control"
                        id="country"
                        name="country"
                        value={formik.values.country}
                        onChange={formik.handleChange}
                        readOnly
                        style={{ borderRadius: '10px', backgroundColor: '#f8f9fa' }}
                      />
                      <label htmlFor="country" className="fw-semibold">
                        <FaGlobe className="me-2 text-info" />
                        Country
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Business Terms */}
            <div className="card border-0 shadow-lg mb-4" style={{ borderRadius: '15px' }}>
              <div className="card-header border-0" style={{
                background: 'linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)',
                borderRadius: '15px 15px 0 0'
              }}>
                <div className="d-flex align-items-center text-white py-2">
                  <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                    <FaCreditCard className="text-white" size={20} />
                  </div>
                  <h5 className="mb-0 fw-bold text-white">Business Terms</h5>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="row g-4">
                  <div className="col-12">
                    <div className="form-floating">
                      <input
                        type="number"
                        className={`form-control ${formik.touched.creditLimit && formik.errors.creditLimit ? 'is-invalid' : ''}`}
                        id="creditLimit"
                        name="creditLimit"
                        value={formik.values.creditLimit}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        placeholder="500000"
                        style={{ borderRadius: '10px' }}
                      />
                      <label htmlFor="creditLimit" className="fw-semibold">
                        <FaRupeeSign className="me-2 text-success" />
                        Credit Limit (₹) *
                      </label>
                      {formik.touched.creditLimit && formik.errors.creditLimit && (
                        <div className="invalid-feedback">{formik.errors.creditLimit}</div>
                      )}
                    </div>
                  </div>
                  <div className="col-12">
                    <div className="form-floating">
                      <select
                        className={`form-select ${formik.touched.paymentTerms && formik.errors.paymentTerms ? 'is-invalid' : ''}`}
                        id="paymentTerms"
                        name="paymentTerms"
                        value={formik.values.paymentTerms}
                        onChange={formik.handleChange}
                        onBlur={formik.handleBlur}
                        style={{ borderRadius: '10px' }}
                      >
                        <option value="15">15 Days</option>
                        <option value="30">30 Days</option>
                        <option value="45">45 Days</option>
                        <option value="60">60 Days</option>
                        <option value="90">90 Days</option>
                      </select>
                      <label htmlFor="paymentTerms" className="fw-semibold">
                        <FaCalendarAlt className="me-2 text-primary" />
                        Payment Terms *
                      </label>
                      {formik.touched.paymentTerms && formik.errors.paymentTerms && (
                        <div className="invalid-feedback">{formik.errors.paymentTerms}</div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Additional Notes */}
            <div className="card border-0 shadow-lg mb-4" style={{ borderRadius: '15px' }}>
              <div className="card-header border-0" style={{
                background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
                borderRadius: '15px 15px 0 0'
              }}>
                <div className="d-flex align-items-center text-white py-2">
                  <div className="bg-white bg-opacity-20 rounded-circle p-2 me-3">
                    <FaFileAlt className="text-white" size={20} />
                  </div>
                  <h5 className="mb-0 fw-bold text-white">Additional Notes</h5>
                </div>
              </div>
              <div className="card-body p-4">
                <div className="form-floating">
                  <textarea
                    className="form-control"
                    id="notes"
                    name="notes"
                    rows="4"
                    value={formik.values.notes}
                    onChange={formik.handleChange}
                    placeholder="Any additional notes or special instructions..."
                    style={{ borderRadius: '10px', minHeight: '120px' }}
                  />
                  <label htmlFor="notes" className="fw-semibold">
                    <FaFileAlt className="me-2 text-info" />
                    Special Instructions & Notes
                  </label>
                </div>
              </div>
            </div>

            {/* Action Buttons */}
            <div className="card border-0 shadow-lg" style={{ borderRadius: '15px' }}>
              <div className="card-body p-4">
                <div className="d-grid gap-3">
                  <button
                    type="submit"
                    className="btn btn-lg text-white fw-bold"
                    disabled={isSubmitting}
                    style={{
                      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
                      border: 'none',
                      borderRadius: '12px',
                      padding: '15px',
                      boxShadow: '0 8px 25px rgba(102, 126, 234, 0.3)'
                    }}
                  >
                    {isSubmitting ? (
                      <>
                        <div className="spinner-border spinner-border-sm me-2" role="status">
                          <span className="visually-hidden">Loading...</span>
                        </div>
                        Creating Customer...
                      </>
                    ) : (
                      <>
                        <FaCheckCircle className="me-2" />
                        Create Customer Profile
                      </>
                    )}
                  </button>
                  <Link
                    to={ROUTES.CUSTOMERS}
                    className="btn btn-outline-secondary btn-lg fw-semibold"
                    style={{ borderRadius: '12px', padding: '15px' }}
                  >
                    <FaArrowLeft className="me-2" />
                    Cancel & Go Back
                  </Link>
                </div>

                {/* Quick Tips */}
                <div className="mt-4 p-3 bg-light rounded-3">
                  <h6 className="fw-bold text-primary mb-2">
                    <FaIndustry className="me-2" />
                    Quick Tips
                  </h6>
                  <ul className="list-unstyled mb-0 small text-muted">
                    <li className="mb-1">• Ensure GST number is valid for tax compliance</li>
                    <li className="mb-1">• Set appropriate credit limits based on business relationship</li>
                    <li>• Add detailed notes for better customer service</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  );
};

export default CreateCustomer;
